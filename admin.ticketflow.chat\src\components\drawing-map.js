import {
  Map,
  Marker,
  Polygon,
  Polyline,
} from 'google-maps-react';
import React from 'react';
import { useState, useEffect, useRef, useCallback } from 'react';
import { BiCurrentLocation } from 'react-icons/bi';
import getMapApiKey from 'helpers/getMapApiKey';
import getDefaultCenter from 'helpers/getDefaultCenter';
import { withGoogleMaps } from './GoogleMapsWrapper';

const mapApiKey = getMapApiKey();
const defaultCenter = getDefaultCenter();

const DrawingManager = (props) => {
  // Ensure triangleCoords is always an array and transform to Google Maps format
  const rawCoords = Array.isArray(props.triangleCoords) ? props.triangleCoords : [];

  // Transform coordinates to ensure they have the correct format for Google Maps
  const validTriangleCoords = rawCoords.map(coord => {
    if (coord && typeof coord === 'object' && coord.lat !== undefined && coord.lng !== undefined) {
      return {
        lat: Number(coord.lat),
        lng: Number(coord.lng)
      };
    }
    return coord;
  }).filter(coord => coord && typeof coord === 'object' && !isNaN(coord.lat) && !isNaN(coord.lng));



  const [markers, setMarkers] = useState(validTriangleCoords);
  const [center, setCenter] = useState(defaultCenter);
  const [polygon, setPolygon] = useState(validTriangleCoords);
  const [finish, setFinish] = useState(validTriangleCoords.length > 0);
  const [focus, setFocus] = useState(false);
  const mapRef = useRef(null);
  const isMountedRef = useRef(true);



  // Track component mounting state
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Update markers when triangleCoords prop changes
  useEffect(() => {
    if (isMountedRef.current) {
      setMarkers(validTriangleCoords);
    }
  }, [validTriangleCoords]);

  useEffect(() => {
    if (isMountedRef.current && props.setMerge) {
      props.setMerge(finish);
    }
  }, [finish, props]);

  const onClick = (t, map, cord) => {
    if (!isMountedRef.current) return;

    setFocus(false);
    const { latLng } = cord;
    const lat = latLng.lat();
    const lng = latLng.lng();
    if (finish) {
      setPolygon([]);
      props.settriangleCoords([{ lat, lng }]);
      setCenter({ lat, lng });
      setFinish(false);
    } else {
      props.settriangleCoords((prev) => [...prev, { lat, lng }]);
    }
  };

  const onFinish = (e) => {
    if (!isMountedRef.current) return;

    setFinish(validTriangleCoords.length > 0);
    if (
      validTriangleCoords[0]?.lat === e.position?.lat &&
      validTriangleCoords.length > 1
    ) {
      setPolygon(validTriangleCoords);
      props.setLocation && props.setLocation(validTriangleCoords);
      setFinish(true);
      setFocus(true);
    }
  };

  const currentLocation = () => {
    if (!isMountedRef.current) return;

    navigator.geolocation.getCurrentPosition(
      function (position) {
        if (isMountedRef.current) {
          setCenter({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          });
        }
      },
      function (error) {
        console.error('Error getting current location:', error);
      }
    );
  };

  useEffect(() => {
    setFocus(true);
  }, []);

  function handleMapReady(_, map) {
    if (!isMountedRef.current || !map) return;

    mapRef.current = map;
    map.setOptions({
      draggableCursor: 'crosshair',
      draggingCursor: 'grab',
    });
  }



  let bounds = null;
  if (props.google && props.google.maps) {
    bounds = new props.google.maps.LatLngBounds();
    if (markers.length > 0) {
      for (var i = 0; i < markers.length; i++) {
        bounds.extend(markers[i]);
      }
    } else {
      bounds.extend(center);
    }
  }

  const OPTIONS = {
    minZoom: 15,
    maxZoom: 15,
  };

  // Show loading if Google Maps is not ready
  if (!props.google || !props.google.maps) {
    return (
      <div className='map-container' style={{ height: 500, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>Loading Google Maps...</div>
      </div>
    );
  }

  return (
    <div className='map-container' style={{ height: 500, width: '100%' }}>
      <button
        className='map-button'
        type='button'
        onClick={() => {
          currentLocation();
        }}
      >
        <BiCurrentLocation />
      </button>
      <Map
        options={OPTIONS}
        cursor='pointer'
        onClick={onClick}
        maxZoom={16}
        minZoom={2}
        google={props.google}
        initialCenter={defaultCenter}
        center={center}
        onReady={handleMapReady}
        bounds={focus && bounds && markers.length > 0 ? bounds : undefined}
        className='clickable'
      >
        {validTriangleCoords.length > 0 && validTriangleCoords.map((item, idx) => (
          <Marker
            onClick={(e) => onFinish(e)}
            key={idx}
            position={item}
            icon={{
              url: 'https://upload.wikimedia.org/wikipedia/commons/9/94/Circle-image.svg',
              scaledSize: props.google?.maps ? new props.google.maps.Size(10, 10) : undefined,
            }}
            className='marker'
          />
        ))}

        {validTriangleCoords.length > 0 && (
          !polygon?.length ? (
            <Polyline
              key={validTriangleCoords.length}
              path={validTriangleCoords}
              strokeColor='black'
              strokeOpacity={0.8}
              strokeWeight={3}
              fillColor='black'
              fillOpacity={0.35}

            />
          ) : (
            <Polygon
              key={polygon.length}
              path={validTriangleCoords}
              strokeColor='black'
              strokeOpacity={0.8}
              strokeWeight={3}
              fillColor='black'
              fillOpacity={0.35}

            />
          )
        )}
      </Map>
    </div>
  );
};

export default withGoogleMaps(DrawingManager, {
  LoadingContainer: () => <div>Loading Google Maps...</div>,
  ErrorContainer: ({ error }) => (
    <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
      Error loading Google Maps: {error?.message || 'Unknown error'}
    </div>
  ),
});
