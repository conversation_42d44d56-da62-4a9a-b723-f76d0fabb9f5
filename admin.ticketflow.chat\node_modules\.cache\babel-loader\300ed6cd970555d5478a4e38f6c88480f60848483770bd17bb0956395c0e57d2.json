{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\privacy\\\\terms.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { toast } from 'react-toastify';\nimport { Button, Card, Col, Form, Input, Row, Space } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { disableRefetch, setMenuData } from '../../redux/slices/menu';\nimport privacyService from '../../services/privacy';\nimport { useTranslation } from 'react-i18next';\nimport Loading from '../../components/loading';\nimport LanguageList from '../../components/language-list';\nimport getTranslationFields from '../../helpers/getTranslationFields';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Terms() {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const {\n    languages,\n    defaultLang\n  } = useSelector(state => state.formLang, shallowEqual);\n  useEffect(() => {\n    return () => {\n      const data = form.getFieldsValue(true);\n      dispatch(setMenuData({\n        activeMenu,\n        data\n      }));\n    };\n  }, []);\n  function getLanguageFields(data) {\n    if (!data) {\n      return {};\n    }\n    const {\n      translations\n    } = data;\n    const result = languages.map(item => {\n      var _translations$find, _translations$find2;\n      return {\n        [`title[${item.locale}]`]: (_translations$find = translations.find(el => el.locale === item.locale)) === null || _translations$find === void 0 ? void 0 : _translations$find.title,\n        [`description[${item.locale}]`]: (_translations$find2 = translations.find(el => el.locale === item.locale)) === null || _translations$find2 === void 0 ? void 0 : _translations$find2.description\n      };\n    });\n    return Object.assign({}, ...result);\n  }\n  function fetchTerms() {\n    setLoading(true);\n    privacyService.getTerms().then(({\n      data\n    }) => form.setFieldsValue({\n      ...getLanguageFields(data)\n    })).finally(() => {\n      setLoading(false);\n      dispatch(disableRefetch(activeMenu));\n    });\n  }\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchTerms();\n    }\n  }, [activeMenu.refetch]);\n  const onFinish = values => {\n    const body = {\n      title: getTranslationFields(languages, values),\n      description: getTranslationFields(languages, values, 'description')\n    };\n    setLoadingBtn(true);\n    privacyService.createTerms(body).then(() => {\n      toast.success(t('successfully.saved'));\n    }).finally(() => setLoadingBtn(false));\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: t('terms'),\n    extra: /*#__PURE__*/_jsxDEV(LanguageList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 37\n    }, this),\n    children: !loading ? /*#__PURE__*/_jsxDEV(Form, {\n      name: \"terms-form\",\n      layout: \"vertical\",\n      onFinish: onFinish,\n      form: form,\n      initialValues: activeMenu.data,\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 12,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: languages.map(item => /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: t('title'),\n            name: `title[${item.locale}]`,\n            rules: [{\n              required: item.locale === defaultLang,\n              message: t('required')\n            }],\n            hidden: item.locale !== defaultLang,\n            children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 19\n            }, this)\n          }, 'title' + item.locale, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: languages.map(item => /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: t('description'),\n            name: `description[${item.locale}]`,\n            valuePropName: \"data\",\n            getValueFromEvent: (event, editor) => {\n              const data = editor.getData();\n              return data;\n            },\n            rules: [{\n              required: item.locale === defaultLang,\n              message: t('required')\n            }],\n            hidden: item.locale !== defaultLang,\n            children: /*#__PURE__*/_jsxDEV(CKEditor, {\n              editor: ClassicEditor\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 19\n            }, this)\n          }, `description-${item.locale}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loadingBtn,\n          children: t('save')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n}\n_s(Terms, \"5IZ63ZVRBbqQSuI+pqmJorzhy0k=\", false, function () {\n  return [useTranslation, useSelector, useDispatch, Form.useForm, useSelector];\n});\n_c = Terms;\nvar _c;\n$RefreshReg$(_c, \"Terms\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "toast", "<PERSON><PERSON>", "Card", "Col", "Form", "Input", "Row", "Space", "shallowEqual", "useDispatch", "useSelector", "CKEditor", "ClassicEditor", "disable<PERSON><PERSON><PERSON><PERSON>", "setMenuData", "privacyService", "useTranslation", "Loading", "LanguageList", "getTranslationFields", "jsxDEV", "_jsxDEV", "Terms", "_s", "t", "activeMenu", "state", "menu", "dispatch", "form", "useForm", "loadingBtn", "setLoadingBtn", "loading", "setLoading", "languages", "defaultLang", "formLang", "data", "getFieldsValue", "getLanguageFields", "translations", "result", "map", "item", "_translations$find", "_translations$find2", "locale", "find", "el", "title", "description", "Object", "assign", "fetchTerms", "getTerms", "then", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finally", "refetch", "onFinish", "values", "body", "createTerms", "success", "extra", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "name", "layout", "initialValues", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "message", "hidden", "valuePropName", "getValueFromEvent", "event", "editor", "getData", "type", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/privacy/terms.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { toast } from 'react-toastify';\nimport { Button, Card, Col, Form, Input, Row, Space } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { disableRefetch, setMenuData } from '../../redux/slices/menu';\nimport privacyService from '../../services/privacy';\nimport { useTranslation } from 'react-i18next';\nimport Loading from '../../components/loading';\nimport LanguageList from '../../components/language-list';\nimport getTranslationFields from '../../helpers/getTranslationFields';\n\nexport default function Terms() {\n  const { t } = useTranslation();\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const { languages, defaultLang } = useSelector(\n    (state) => state.formLang,\n    shallowEqual\n  );\n\n  useEffect(() => {\n    return () => {\n      const data = form.getFieldsValue(true);\n      dispatch(setMenuData({ activeMenu, data }));\n    };\n  }, []);\n\n  function getLanguageFields(data) {\n    if (!data) {\n      return {};\n    }\n    const { translations } = data;\n    const result = languages.map((item) => ({\n      [`title[${item.locale}]`]: translations.find(\n        (el) => el.locale === item.locale\n      )?.title,\n      [`description[${item.locale}]`]: translations.find(\n        (el) => el.locale === item.locale\n      )?.description,\n    }));\n    return Object.assign({}, ...result);\n  }\n\n  function fetchTerms() {\n    setLoading(true);\n    privacyService\n      .getTerms()\n      .then(({ data }) =>\n        form.setFieldsValue({\n          ...getLanguageFields(data),\n        })\n      )\n      .finally(() => {\n        setLoading(false);\n        dispatch(disableRefetch(activeMenu));\n      });\n  }\n\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchTerms();\n    }\n  }, [activeMenu.refetch]);\n\n  const onFinish = (values) => {\n    const body = {\n      title: getTranslationFields(languages, values),\n      description: getTranslationFields(languages, values, 'description'),\n    };\n    setLoadingBtn(true);\n    privacyService\n      .createTerms(body)\n      .then(() => {\n        toast.success(t('successfully.saved'));\n      })\n      .finally(() => setLoadingBtn(false));\n  };\n\n  return (\n    <Card title={t('terms')} extra={<LanguageList />}>\n      {!loading ? (\n        <Form\n          name='terms-form'\n          layout='vertical'\n          onFinish={onFinish}\n          form={form}\n          initialValues={activeMenu.data}\n        >\n          <Row gutter={12}>\n            <Col span={12}>\n              {languages.map((item) => (\n                <Form.Item\n                  key={'title' + item.locale}\n                  label={t('title')}\n                  name={`title[${item.locale}]`}\n                  rules={[\n                    {\n                      required: item.locale === defaultLang,\n                      message: t('required'),\n                    },\n                  ]}\n                  hidden={item.locale !== defaultLang}\n                >\n                  <Input />\n                </Form.Item>\n              ))}\n            </Col>\n            <Col span={24}>\n              {languages.map((item) => (\n                <Form.Item\n                  key={`description-${item.locale}`}\n                  label={t('description')}\n                  name={`description[${item.locale}]`}\n                  valuePropName='data'\n                  getValueFromEvent={(event, editor) => {\n                    const data = editor.getData();\n                    return data;\n                  }}\n                  rules={[\n                    {\n                      required: item.locale === defaultLang,\n                      message: t('required'),\n                    },\n                  ]}\n                  hidden={item.locale !== defaultLang}\n                >\n                  <CKEditor editor={ClassicEditor} />\n                </Form.Item>\n              ))}\n            </Col>\n          </Row>\n          <Space>\n            <Button type='primary' htmlType='submit' loading={loadingBtn}>\n              {t('save')}\n            </Button>\n          </Space>\n        </Form>\n      ) : (\n        <Loading />\n      )}\n    </Card>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AACjE,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AACrE,OAAOC,cAAc,MAAM,wBAAwB;AACnD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,eAAe,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAE,CAAC,GAAGR,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAW,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEnB,YAAY,CAAC;EACvE,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,IAAI,CAAC,GAAGzB,IAAI,CAAC0B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEoC,SAAS;IAAEC;EAAY,CAAC,GAAG1B,WAAW,CAC3CgB,KAAK,IAAKA,KAAK,CAACW,QAAQ,EACzB7B,YACF,CAAC;EAEDV,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,MAAMwC,IAAI,GAAGT,IAAI,CAACU,cAAc,CAAC,IAAI,CAAC;MACtCX,QAAQ,CAACd,WAAW,CAAC;QAAEW,UAAU;QAAEa;MAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,SAASE,iBAAiBA,CAACF,IAAI,EAAE;IAC/B,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,CAAC,CAAC;IACX;IACA,MAAM;MAAEG;IAAa,CAAC,GAAGH,IAAI;IAC7B,MAAMI,MAAM,GAAGP,SAAS,CAACQ,GAAG,CAAEC,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA;MAAA,OAAM;QACtC,CAAE,SAAQF,IAAI,CAACG,MAAO,GAAE,IAAAF,kBAAA,GAAGJ,YAAY,CAACO,IAAI,CACzCC,EAAE,IAAKA,EAAE,CAACF,MAAM,KAAKH,IAAI,CAACG,MAC7B,CAAC,cAAAF,kBAAA,uBAF0BA,kBAAA,CAExBK,KAAK;QACR,CAAE,eAAcN,IAAI,CAACG,MAAO,GAAE,IAAAD,mBAAA,GAAGL,YAAY,CAACO,IAAI,CAC/CC,EAAE,IAAKA,EAAE,CAACF,MAAM,KAAKH,IAAI,CAACG,MAC7B,CAAC,cAAAD,mBAAA,uBAFgCA,mBAAA,CAE9BK;MACL,CAAC;IAAA,CAAC,CAAC;IACH,OAAOC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGX,MAAM,CAAC;EACrC;EAEA,SAASY,UAAUA,CAAA,EAAG;IACpBpB,UAAU,CAAC,IAAI,CAAC;IAChBnB,cAAc,CACXwC,QAAQ,CAAC,CAAC,CACVC,IAAI,CAAC,CAAC;MAAElB;IAAK,CAAC,KACbT,IAAI,CAAC4B,cAAc,CAAC;MAClB,GAAGjB,iBAAiB,CAACF,IAAI;IAC3B,CAAC,CACH,CAAC,CACAoB,OAAO,CAAC,MAAM;MACbxB,UAAU,CAAC,KAAK,CAAC;MACjBN,QAAQ,CAACf,cAAc,CAACY,UAAU,CAAC,CAAC;IACtC,CAAC,CAAC;EACN;EAEA3B,SAAS,CAAC,MAAM;IACd,IAAI2B,UAAU,CAACkC,OAAO,EAAE;MACtBL,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC7B,UAAU,CAACkC,OAAO,CAAC,CAAC;EAExB,MAAMC,QAAQ,GAAIC,MAAM,IAAK;IAC3B,MAAMC,IAAI,GAAG;MACXZ,KAAK,EAAE/B,oBAAoB,CAACgB,SAAS,EAAE0B,MAAM,CAAC;MAC9CV,WAAW,EAAEhC,oBAAoB,CAACgB,SAAS,EAAE0B,MAAM,EAAE,aAAa;IACpE,CAAC;IACD7B,aAAa,CAAC,IAAI,CAAC;IACnBjB,cAAc,CACXgD,WAAW,CAACD,IAAI,CAAC,CACjBN,IAAI,CAAC,MAAM;MACVxD,KAAK,CAACgE,OAAO,CAACxC,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACxC,CAAC,CAAC,CACDkC,OAAO,CAAC,MAAM1B,aAAa,CAAC,KAAK,CAAC,CAAC;EACxC,CAAC;EAED,oBACEX,OAAA,CAACnB,IAAI;IAACgD,KAAK,EAAE1B,CAAC,CAAC,OAAO,CAAE;IAACyC,KAAK,eAAE5C,OAAA,CAACH,YAAY;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IAAAC,QAAA,EAC9C,CAACrC,OAAO,gBACPZ,OAAA,CAACjB,IAAI;MACHmE,IAAI,EAAC,YAAY;MACjBC,MAAM,EAAC,UAAU;MACjBZ,QAAQ,EAAEA,QAAS;MACnB/B,IAAI,EAAEA,IAAK;MACX4C,aAAa,EAAEhD,UAAU,CAACa,IAAK;MAAAgC,QAAA,gBAE/BjD,OAAA,CAACf,GAAG;QAACoE,MAAM,EAAE,EAAG;QAAAJ,QAAA,gBACdjD,OAAA,CAAClB,GAAG;UAACwE,IAAI,EAAE,EAAG;UAAAL,QAAA,EACXnC,SAAS,CAACQ,GAAG,CAAEC,IAAI,iBAClBvB,OAAA,CAACjB,IAAI,CAACwE,IAAI;YAERC,KAAK,EAAErD,CAAC,CAAC,OAAO,CAAE;YAClB+C,IAAI,EAAG,SAAQ3B,IAAI,CAACG,MAAO,GAAG;YAC9B+B,KAAK,EAAE,CACL;cACEC,QAAQ,EAAEnC,IAAI,CAACG,MAAM,KAAKX,WAAW;cACrC4C,OAAO,EAAExD,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YACFyD,MAAM,EAAErC,IAAI,CAACG,MAAM,KAAKX,WAAY;YAAAkC,QAAA,eAEpCjD,OAAA,CAAChB,KAAK;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAXJ,OAAO,GAAGzB,IAAI,CAACG,MAAM;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYjB,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAAClB,GAAG;UAACwE,IAAI,EAAE,EAAG;UAAAL,QAAA,EACXnC,SAAS,CAACQ,GAAG,CAAEC,IAAI,iBAClBvB,OAAA,CAACjB,IAAI,CAACwE,IAAI;YAERC,KAAK,EAAErD,CAAC,CAAC,aAAa,CAAE;YACxB+C,IAAI,EAAG,eAAc3B,IAAI,CAACG,MAAO,GAAG;YACpCmC,aAAa,EAAC,MAAM;YACpBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;cACpC,MAAM/C,IAAI,GAAG+C,MAAM,CAACC,OAAO,CAAC,CAAC;cAC7B,OAAOhD,IAAI;YACb,CAAE;YACFwC,KAAK,EAAE,CACL;cACEC,QAAQ,EAAEnC,IAAI,CAACG,MAAM,KAAKX,WAAW;cACrC4C,OAAO,EAAExD,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YACFyD,MAAM,EAAErC,IAAI,CAACG,MAAM,KAAKX,WAAY;YAAAkC,QAAA,eAEpCjD,OAAA,CAACV,QAAQ;cAAC0E,MAAM,EAAEzE;YAAc;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAhB7B,eAAczB,IAAI,CAACG,MAAO,EAAC;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBxB,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhD,OAAA,CAACd,KAAK;QAAA+D,QAAA,eACJjD,OAAA,CAACpB,MAAM;UAACsF,IAAI,EAAC,SAAS;UAACC,QAAQ,EAAC,QAAQ;UAACvD,OAAO,EAAEF,UAAW;UAAAuC,QAAA,EAC1D9C,CAAC,CAAC,MAAM;QAAC;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAEPhD,OAAA,CAACJ,OAAO;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX;AAAC9C,EAAA,CAtIuBD,KAAK;EAAA,QACbN,cAAc,EACLN,WAAW,EACjBD,WAAW,EACbL,IAAI,CAAC0B,OAAO,EAGQpB,WAAW;AAAA;AAAA+E,EAAA,GAPxBnE,KAAK;AAAA,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}