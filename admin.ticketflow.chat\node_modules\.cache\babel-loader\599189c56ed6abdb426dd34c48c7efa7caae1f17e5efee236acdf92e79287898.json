{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\map.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { GoogleApiWrapper, Map, Marker } from 'google-maps-react';\nimport pinIcon from 'assets/images/pin.png';\nimport getAddressFromLocation from 'helpers/getAddressFromLocation';\nimport { BiCurrentLocation } from 'react-icons/bi';\nimport { toast } from 'react-toastify';\nimport { useTranslation } from 'react-i18next';\nimport getMapApiKey from 'helpers/getMapApiKey';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst mapApiKey = getMapApiKey();\nfunction GoogleMap(props) {\n  _s();\n  var _props$location, _props$location2;\n  const [loc, setLoc] = useState();\n  const {\n    t\n  } = useTranslation();\n  const isMountedRef = useRef(true);\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  const onClickMap = async (t, map, coord) => {\n    const {\n      latLng\n    } = coord;\n    const location = {\n      lat: latLng.lat(),\n      lng: latLng.lng()\n    };\n    props.setLocation(location);\n    const address = await getAddressFromLocation(location, mapApiKey);\n    props.setAddress(address);\n  };\n  const handleSubmit = async event => {\n    const location = {\n      lat: event === null || event === void 0 ? void 0 : event.lat,\n      lng: event === null || event === void 0 ? void 0 : event.lng\n    };\n    props.setLocation(location);\n    const address = await getAddressFromLocation(location, mapApiKey);\n    props.setAddress(address);\n  };\n  const currentLocation = async () => {\n    await navigator.geolocation.getCurrentPosition(function (position) {\n      const location = {\n        lat: position.coords.latitude,\n        lng: position.coords.longitude\n      };\n      setLoc(location);\n    }, function (error) {\n      toast.warning(t('turn.on.gps'));\n    });\n  };\n  useEffect(() => {\n    currentLocation();\n    // eslint-disable-next-line\n  }, []);\n  const markers = [{\n    lat: Number(props === null || props === void 0 ? void 0 : (_props$location = props.location) === null || _props$location === void 0 ? void 0 : _props$location.lat) || 0,\n    lng: Number(props === null || props === void 0 ? void 0 : (_props$location2 = props.location) === null || _props$location2 === void 0 ? void 0 : _props$location2.lng) || 0\n  }];\n  var bounds = new props.google.maps.LatLngBounds();\n  for (var i = 0; i < markers.length; i++) {\n    bounds.extend(markers[i]);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"map-container\",\n    style: {\n      height: 400,\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"map-button\",\n      type: \"button\",\n      onClick: () => {\n        currentLocation();\n        if (loc) {\n          handleSubmit(loc);\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(BiCurrentLocation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Map, {\n      cursor: 'pointer',\n      onClick: onClickMap,\n      google: props.google,\n      defaultZoom: 12,\n      zoom: 10,\n      className: \"clickable\",\n      initialCenter: props.location,\n      center: props.location\n      // bounds={bounds}\n      ,\n      children: /*#__PURE__*/_jsxDEV(Marker, {\n        position: props.location,\n        icon: {\n          url: pinIcon,\n          scaledSize: new props.google.maps.Size(32, 32)\n        },\n        className: \"marker\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n}\n_s(GoogleMap, \"R+eWtc1oNBAEnC2gbPWncB+y788=\", false, function () {\n  return [useTranslation];\n});\n_c = GoogleMap;\nexport default GoogleApiWrapper({\n  apiKey: mapApiKey,\n  libraries: ['places', 'geometry']\n})(GoogleMap);\nvar _c;\n$RefreshReg$(_c, \"GoogleMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "GoogleApiWrapper", "Map", "<PERSON><PERSON>", "pinIcon", "getAddressFromLocation", "BiCurrentLocation", "toast", "useTranslation", "getMapApiKey", "jsxDEV", "_jsxDEV", "mapApiKey", "GoogleMap", "props", "_s", "_props$location", "_props$location2", "loc", "setLoc", "t", "isMountedRef", "current", "onClickMap", "map", "coord", "latLng", "location", "lat", "lng", "setLocation", "address", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "event", "currentLocation", "navigator", "geolocation", "getCurrentPosition", "position", "coords", "latitude", "longitude", "error", "warning", "markers", "Number", "bounds", "google", "maps", "LatLngBounds", "i", "length", "extend", "className", "style", "height", "width", "children", "type", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cursor", "defaultZoom", "zoom", "initialCenter", "center", "icon", "url", "scaledSize", "Size", "_c", "<PERSON><PERSON><PERSON><PERSON>", "libraries", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/map.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { GoogleApiWrapper, Map, Marker } from 'google-maps-react';\nimport pinIcon from 'assets/images/pin.png';\nimport getAddressFromLocation from 'helpers/getAddressFromLocation';\nimport { BiCurrentLocation } from 'react-icons/bi';\nimport { toast } from 'react-toastify';\nimport { useTranslation } from 'react-i18next';\nimport getMapApiKey from 'helpers/getMapApiKey';\n\nconst mapApiKey = getMapApiKey();\n\nfunction GoogleMap(props) {\n  const [loc, setLoc] = useState();\n  const { t } = useTranslation();\n  const isMountedRef = useRef(true);\n\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  const onClickMap = async (t, map, coord) => {\n    const { latLng } = coord;\n    const location = {\n      lat: latLng.lat(),\n      lng: latLng.lng(),\n    };\n    props.setLocation(location);\n    const address = await getAddressFromLocation(location, mapApiKey);\n    props.setAddress(address);\n  };\n\n  const handleSubmit = async (event) => {\n    const location = {\n      lat: event?.lat,\n      lng: event?.lng,\n    };\n    props.setLocation(location);\n    const address = await getAddressFromLocation(location, mapApiKey);\n    props.setAddress(address);\n  };\n\n  const currentLocation = async () => {\n    await navigator.geolocation.getCurrentPosition(\n      function (position) {\n        const location = {\n          lat: position.coords.latitude,\n          lng: position.coords.longitude,\n        };\n        setLoc(location);\n      },\n      function (error) {\n        toast.warning(t('turn.on.gps'));\n      },\n    );\n  };\n\n  useEffect(() => {\n    currentLocation();\n    // eslint-disable-next-line\n  }, []);\n\n  const markers = [\n    {\n      lat: Number(props?.location?.lat) || 0,\n      lng: Number(props?.location?.lng) || 0,\n    },\n  ];\n\n  var bounds = new props.google.maps.LatLngBounds();\n  for (var i = 0; i < markers.length; i++) {\n    bounds.extend(markers[i]);\n  }\n  return (\n    <div className='map-container' style={{ height: 400, width: '100%' }}>\n      <button\n        className='map-button'\n        type='button'\n        onClick={() => {\n          currentLocation();\n          if (loc) {\n            handleSubmit(loc);\n          }\n        }}\n      >\n        <BiCurrentLocation />\n      </button>\n      <Map\n        cursor={'pointer'}\n        onClick={onClickMap}\n        google={props.google}\n        defaultZoom={12}\n        zoom={10}\n        className='clickable'\n        initialCenter={props.location}\n        center={props.location}\n        // bounds={bounds}\n      >\n        <Marker\n          position={props.location}\n          icon={{\n            url: pinIcon,\n            scaledSize: new props.google.maps.Size(32, 32),\n          }}\n          className='marker'\n        />\n      </Map>\n    </div>\n  );\n}\n\nexport default GoogleApiWrapper({\n  apiKey: mapApiKey,\n  libraries: ['places', 'geometry'],\n})(GoogleMap);\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,gBAAgB,EAAEC,GAAG,EAAEC,MAAM,QAAQ,mBAAmB;AACjE,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,sBAAsB,MAAM,gCAAgC;AACnE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,SAAS,GAAGH,YAAY,CAAC,CAAC;AAEhC,SAASI,SAASA,CAACC,KAAK,EAAE;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACxB,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EAChC,MAAM;IAAEqB;EAAE,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAC9B,MAAMa,YAAY,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAEjCF,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXuB,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAG,MAAAA,CAAOH,CAAC,EAAEI,GAAG,EAAEC,KAAK,KAAK;IAC1C,MAAM;MAAEC;IAAO,CAAC,GAAGD,KAAK;IACxB,MAAME,QAAQ,GAAG;MACfC,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MACjBC,GAAG,EAAEH,MAAM,CAACG,GAAG,CAAC;IAClB,CAAC;IACDf,KAAK,CAACgB,WAAW,CAACH,QAAQ,CAAC;IAC3B,MAAMI,OAAO,GAAG,MAAM1B,sBAAsB,CAACsB,QAAQ,EAAEf,SAAS,CAAC;IACjEE,KAAK,CAACkB,UAAU,CAACD,OAAO,CAAC;EAC3B,CAAC;EAED,MAAME,YAAY,GAAG,MAAOC,KAAK,IAAK;IACpC,MAAMP,QAAQ,GAAG;MACfC,GAAG,EAAEM,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEN,GAAG;MACfC,GAAG,EAAEK,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEL;IACd,CAAC;IACDf,KAAK,CAACgB,WAAW,CAACH,QAAQ,CAAC;IAC3B,MAAMI,OAAO,GAAG,MAAM1B,sBAAsB,CAACsB,QAAQ,EAAEf,SAAS,CAAC;IACjEE,KAAK,CAACkB,UAAU,CAACD,OAAO,CAAC;EAC3B,CAAC;EAED,MAAMI,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,MAAMC,SAAS,CAACC,WAAW,CAACC,kBAAkB,CAC5C,UAAUC,QAAQ,EAAE;MAClB,MAAMZ,QAAQ,GAAG;QACfC,GAAG,EAAEW,QAAQ,CAACC,MAAM,CAACC,QAAQ;QAC7BZ,GAAG,EAAEU,QAAQ,CAACC,MAAM,CAACE;MACvB,CAAC;MACDvB,MAAM,CAACQ,QAAQ,CAAC;IAClB,CAAC,EACD,UAAUgB,KAAK,EAAE;MACfpC,KAAK,CAACqC,OAAO,CAACxB,CAAC,CAAC,aAAa,CAAC,CAAC;IACjC,CACF,CAAC;EACH,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACdqC,eAAe,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,OAAO,GAAG,CACd;IACEjB,GAAG,EAAEkB,MAAM,CAAChC,KAAK,aAALA,KAAK,wBAAAE,eAAA,GAALF,KAAK,CAAEa,QAAQ,cAAAX,eAAA,uBAAfA,eAAA,CAAiBY,GAAG,CAAC,IAAI,CAAC;IACtCC,GAAG,EAAEiB,MAAM,CAAChC,KAAK,aAALA,KAAK,wBAAAG,gBAAA,GAALH,KAAK,CAAEa,QAAQ,cAAAV,gBAAA,uBAAfA,gBAAA,CAAiBY,GAAG,CAAC,IAAI;EACvC,CAAC,CACF;EAED,IAAIkB,MAAM,GAAG,IAAIjC,KAAK,CAACkC,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC,CAAC;EACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,OAAO,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCJ,MAAM,CAACM,MAAM,CAACR,OAAO,CAACM,CAAC,CAAC,CAAC;EAC3B;EACA,oBACExC,OAAA;IAAK2C,SAAS,EAAC,eAAe;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACnE/C,OAAA;MACE2C,SAAS,EAAC,YAAY;MACtBK,IAAI,EAAC,QAAQ;MACbC,OAAO,EAAEA,CAAA,KAAM;QACbzB,eAAe,CAAC,CAAC;QACjB,IAAIjB,GAAG,EAAE;UACPe,YAAY,CAACf,GAAG,CAAC;QACnB;MACF,CAAE;MAAAwC,QAAA,eAEF/C,OAAA,CAACL,iBAAiB;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eACTrD,OAAA,CAACT,GAAG;MACF+D,MAAM,EAAE,SAAU;MAClBL,OAAO,EAAErC,UAAW;MACpByB,MAAM,EAAElC,KAAK,CAACkC,MAAO;MACrBkB,WAAW,EAAE,EAAG;MAChBC,IAAI,EAAE,EAAG;MACTb,SAAS,EAAC,WAAW;MACrBc,aAAa,EAAEtD,KAAK,CAACa,QAAS;MAC9B0C,MAAM,EAAEvD,KAAK,CAACa;MACd;MAAA;MAAA+B,QAAA,eAEA/C,OAAA,CAACR,MAAM;QACLoC,QAAQ,EAAEzB,KAAK,CAACa,QAAS;QACzB2C,IAAI,EAAE;UACJC,GAAG,EAAEnE,OAAO;UACZoE,UAAU,EAAE,IAAI1D,KAAK,CAACkC,MAAM,CAACC,IAAI,CAACwB,IAAI,CAAC,EAAE,EAAE,EAAE;QAC/C,CAAE;QACFnB,SAAS,EAAC;MAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjD,EAAA,CAnGQF,SAAS;EAAA,QAEFL,cAAc;AAAA;AAAAkE,EAAA,GAFrB7D,SAAS;AAqGlB,eAAeZ,gBAAgB,CAAC;EAC9B0E,MAAM,EAAE/D,SAAS;EACjBgE,SAAS,EAAE,CAAC,QAAQ,EAAE,UAAU;AAClC,CAAC,CAAC,CAAC/D,SAAS,CAAC;AAAC,IAAA6D,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}