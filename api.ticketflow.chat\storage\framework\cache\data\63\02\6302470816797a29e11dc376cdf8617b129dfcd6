2617137953O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:7:{i:0;O:22:"App\Models\OrderStatus":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"order_statuses";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:7;s:4:"name";s:8:"canceled";s:6:"active";i:1;s:4:"sort";i:7;s:10:"deleted_at";N;}s:11:" * original";a:5:{s:2:"id";i:7;s:4:"name";s:8:"canceled";s:6:"active";i:1;s:4:"sort";i:7;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}i:1;O:22:"App\Models\OrderStatus":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"order_statuses";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:6;s:4:"name";s:9:"delivered";s:6:"active";i:1;s:4:"sort";i:6;s:10:"deleted_at";N;}s:11:" * original";a:5:{s:2:"id";i:6;s:4:"name";s:9:"delivered";s:6:"active";i:1;s:4:"sort";i:6;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}i:2;O:22:"App\Models\OrderStatus":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"order_statuses";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:5;s:4:"name";s:8:"on_a_way";s:6:"active";i:1;s:4:"sort";i:5;s:10:"deleted_at";N;}s:11:" * original";a:5:{s:2:"id";i:5;s:4:"name";s:8:"on_a_way";s:6:"active";i:1;s:4:"sort";i:5;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}i:3;O:22:"App\Models\OrderStatus":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"order_statuses";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:4;s:4:"name";s:5:"ready";s:6:"active";i:1;s:4:"sort";i:4;s:10:"deleted_at";N;}s:11:" * original";a:5:{s:2:"id";i:4;s:4:"name";s:5:"ready";s:6:"active";i:1;s:4:"sort";i:4;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}i:4;O:22:"App\Models\OrderStatus":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"order_statuses";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:3;s:4:"name";s:7:"cooking";s:6:"active";i:1;s:4:"sort";i:3;s:10:"deleted_at";N;}s:11:" * original";a:5:{s:2:"id";i:3;s:4:"name";s:7:"cooking";s:6:"active";i:1;s:4:"sort";i:3;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}i:5;O:22:"App\Models\OrderStatus":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"order_statuses";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:2;s:4:"name";s:8:"accepted";s:6:"active";i:1;s:4:"sort";i:2;s:10:"deleted_at";N;}s:11:" * original";a:5:{s:2:"id";i:2;s:4:"name";s:8:"accepted";s:6:"active";i:1;s:4:"sort";i:2;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}i:6;O:22:"App\Models\OrderStatus":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"order_statuses";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:1;s:4:"name";s:3:"new";s:6:"active";i:1;s:4:"sort";i:1;s:10:"deleted_at";N;}s:11:" * original";a:5:{s:2:"id";i:1;s:4:"name";s:3:"new";s:6:"active";i:1;s:4:"sort";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}