{"__meta": {"id": "Xfa031819901254d651b64ae39f18976a", "datetime": "2025-07-21 19:24:35", "utime": 1753136675.786325, "method": "GET", "uri": "/api/v1/dashboard/admin/products/paginate?lang=pt-BR&page=1&perPage=10", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[19:24:35] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753136675.600007, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753136675.34909, "end": 1753136675.786341, "duration": 0.43725085258483887, "duration_str": "437ms", "measures": [{"label": "Booting", "start": 1753136675.34909, "relative_start": 0, "end": 1753136675.583917, "relative_end": 1753136675.583917, "duration": 0.23482680320739746, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753136675.583931, "relative_start": 0.23484086990356445, "end": 1753136675.786344, "relative_end": 3.0994415283203125e-06, "duration": 0.20241308212280273, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43058896, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/dashboard/admin/products/paginate", "middleware": "api, block.ip, sanctum.check, role:admin|manager", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController@paginate", "as": "admin.", "namespace": null, "prefix": "api/v1/dashboard/admin", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php&line=57\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php:57-66</a>"}, "queries": {"nb_statements": 52, "nb_failed_statements": 0, "accumulated_duration": 0.023650000000000004, "accumulated_duration_str": "23.65ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00304, "duration_str": "3.04ms", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 0, "width_percent": 12.854}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 12.854, "width_percent": 1.564}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 14.419, "width_percent": 2.495}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 16.913, "width_percent": 1.564}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '55' limit 1", "type": "query", "params": [], "bindings": ["55"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 18.478, "width_percent": 2.664}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 21.142, "width_percent": 2.199}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-21 19:24:35', `personal_access_tokens`.`updated_at` = '2025-07-21 19:24:35' where `id` = 55", "type": "query", "params": [], "bindings": ["2025-07-21 19:24:35", "2025-07-21 19:24:35", "55"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 23.34, "width_percent": 5.074}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (101) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 23, "namespace": "middleware", "name": "role", "line": 29}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 18}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 28.414, "width_percent": 2.791}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'products'", "type": "query", "params": [], "bindings": ["foodyman", "products"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Models\\Product.php", "line": 248}, {"index": 19, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 69}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Models\\Product.php:248", "connection": "foodyman", "start_percent": 31.205, "width_percent": 4.651}, {"sql": "select count(*) as aggregate from `products` where `addon` = 0 and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 35.856, "width_percent": 1.268}, {"sql": "select * from `products` where `addon` = 0 and `products`.`deleted_at` is null order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 37.125, "width_percent": 2.03}, {"sql": "select * from `stocks` where `stocks`.`countable_id` in (3, 4, 6, 7) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 39.154, "width_percent": 1.48}, {"sql": "select * from `stock_addons` where `stock_addons`.`stock_id` in (3, 4, 6, 7, 8, 9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 40.634, "width_percent": 1.226}, {"sql": "select * from `products` where `products`.`id` in (5) and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `addon` = 1 and `quantity` > 0 and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null) and `active` = 1 and `status` = 'published' and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "0", "pt-BR", "pt-BR", "1", "published"], "hints": null, "show_copy": false, "backtrace": [{"index": 30, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 31, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 41.86, "width_percent": 2.622}, {"sql": "select * from `stocks` where `stocks`.`countable_id` in (5) and `addon` = 1 and `quantity` > 0 and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 36, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 44.482, "width_percent": 1.099}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (5) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 36, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 45.581, "width_percent": 1.142}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (3, 4, 6, 7, 8, 9) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-21 19:24:35' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-21 19:24:35", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 46.723, "width_percent": 2.03}, {"sql": "select `extra_values`.*, `stock_extras`.`stock_id` as `pivot_stock_id`, `stock_extras`.`extra_value_id` as `pivot_extra_value_id` from `extra_values` inner join `stock_extras` on `extra_values`.`id` = `stock_extras`.`extra_value_id` where `stock_extras`.`stock_id` in (3, 4, 6, 7, 8, 9) and `extra_values`.`deleted_at` is null order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 48.753, "width_percent": 2.326}, {"sql": "select * from `extra_groups` where `extra_groups`.`id` in (4) and `extra_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 29, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 30, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 51.078, "width_percent": 1.268}, {"sql": "select * from `extra_group_translations` where `extra_group_translations`.`extra_group_id` in (4) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `extra_group_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 34, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 35, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 52.347, "width_percent": 1.818}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (3, 4, 6, 7) and `start` <= '2025-07-21 00:00:00' and `end` >= '2025-07-21 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-21 00:00:00", "2025-07-21 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 54.165, "width_percent": 1.903}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (3, 4, 6, 7) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 56.068, "width_percent": 1.311}, {"sql": "select `id`, `status` from `shops` where `shops`.`id` in (502, 503) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 57.378, "width_percent": 1.142}, {"sql": "select `id`, `locale`, `title`, `shop_id` from `shop_translations` where `shop_translations`.`shop_id` in (503) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `shop_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 58.52, "width_percent": 1.226}, {"sql": "select * from `units` where `units`.`id` in (1) and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 59.746, "width_percent": 1.057}, {"sql": "select `id`, `locale`, `title`, `unit_id` from `unit_translations` where `unit_translations`.`unit_id` in (1) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `unit_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 60.803, "width_percent": 1.818}, {"sql": "select * from `reviews` where `reviews`.`reviewable_id` in (3, 4, 6, 7) and `reviews`.`reviewable_type` = 'App\\Models\\Product' and `reviews`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 62.622, "width_percent": 1.311}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (3, 4, 6, 7) and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 63.932, "width_percent": 1.226}, {"sql": "select `id`, `uuid` from `categories` where `categories`.`id` in (25, 26, 27, 29) and `categories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 65.159, "width_percent": 1.649}, {"sql": "select `id`, `category_id`, `locale`, `title` from `category_translations` where `category_translations`.`category_id` in (26, 27, 29) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `category_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 66.808, "width_percent": 1.776}, {"sql": "select * from `kitchens` where `kitchens`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 68.584, "width_percent": 1.142}, {"sql": "select * from `kitchen_translations` where `kitchen_translations`.`kitchen_id` in (3) and (`locale` = 'pt-BR' or `locale` = 'pt-BR')", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ProductRepository\\ProductRepository.php", "line": 97}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ProductController.php", "line": 59}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Repositories\\ProductRepository\\ProductRepository.php:97", "connection": "foodyman", "start_percent": 69.725, "width_percent": 1.184}, {"sql": "select * from `products` where `products`.`id` = 7 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 70.909, "width_percent": 2.579}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 7 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 73.488, "width_percent": 1.438}, {"sql": "select * from `products` where `products`.`id` = 7 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 74.926, "width_percent": 1.438}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 7 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 76.364, "width_percent": 1.353}, {"sql": "select * from `products` where `products`.`id` = 7 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 77.717, "width_percent": 1.395}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 7 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 79.112, "width_percent": 1.353}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 80.465, "width_percent": 1.522}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 81.987, "width_percent": 1.311}, {"sql": "select * from `products` where `products`.`id` = 6 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 83.298, "width_percent": 1.268}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 6 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 84.567, "width_percent": 1.268}, {"sql": "select * from `products` where `products`.`id` = 5 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 85.835, "width_percent": 1.438}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 5 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 87.273, "width_percent": 1.818}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 89.091, "width_percent": 1.353}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 90.444, "width_percent": 1.142}, {"sql": "select * from `products` where `products`.`id` = 4 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 91.586, "width_percent": 1.395}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 4 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 92.981, "width_percent": 1.48}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 94.461, "width_percent": 1.226}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 95.687, "width_percent": 1.311}, {"sql": "select * from `products` where `products`.`id` = 3 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 96.998, "width_percent": 1.353}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 3 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 186}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 98.351, "width_percent": 1.649}]}, "models": {"data": {"App\\Models\\KitchenTranslation": 1, "App\\Models\\Kitchen": 1, "App\\Models\\CategoryTranslation": 3, "App\\Models\\Category": 3, "App\\Models\\Unit": 1, "App\\Models\\ShopTranslation": 1, "App\\Models\\Shop": 1, "App\\Models\\ExtraGroupTranslation": 1, "App\\Models\\ExtraGroup": 1, "App\\Models\\ExtraValue": 4, "App\\Models\\ProductTranslation": 9, "App\\Models\\StockAddon": 1, "App\\Models\\Stock": 7, "App\\Models\\Product": 12, "Spatie\\Permission\\Models\\Role": 1, "App\\Models\\User": 1, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 2, "App\\Models\\Language": 2}, "count": 53}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7264e1-5ec3-4a3c-9ff1-5ccd5f2aeef5\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/admin/products/paginate", "status_code": "<pre class=sf-dump id=sf-dump-1058495730 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1058495730\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-541224375 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>perPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541224375\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-162352494 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>perPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162352494\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-314991929 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314991929\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1792116259 data-indent-pad=\"  \"><span class=sf-dump-note>array:33</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56968</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"70 characters\">/api/v1/dashboard/admin/products/paginate?lang=pt-BR&amp;page=1&amp;perPage=10</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"41 characters\">/api/v1/dashboard/admin/products/paginate</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"51 characters\">/index.php/api/v1/dashboard/admin/products/paginate</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"28 characters\">lang=pt-BR&amp;page=1&amp;perPage=10</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753136675.3491</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753136675</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">lang=pt-BR&amp;page=1&amp;perPage=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792116259\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2019771525 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2019771525\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2132904645 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 22:24:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4996</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132904645\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-526395605 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-526395605\", {\"maxDepth\":0})</script>\n"}}