{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\seller-views\\\\users\\\\components\\\\form\\\\edit\\\\components\\\\deliveryman-zone.js\",\n  _s = $RefreshSig$();\nimport { Button, Col, Form, Row } from 'antd';\nimport MapGif from 'assets/video/map.gif';\nimport DrawingManager from 'components/drawing-map';\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { toast } from 'react-toastify';\nimport userService from 'services/seller/user';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { setRefetch } from 'redux/slices/menu';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DeliverymanZone = ({\n  data\n}) => {\n  _s();\n  var _data$delivery_man_de, _data$delivery_man_de2;\n  const [form] = Form.useForm();\n  const {\n    t\n  } = useTranslation();\n  const dispatch = useDispatch();\n  const isMountedRef = useRef(true);\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const [triangleCoords, setTriangleCoords] = useState(data !== null && data !== void 0 && (_data$delivery_man_de = data.delivery_man_delivery_zone) !== null && _data$delivery_man_de !== void 0 && _data$delivery_man_de.length ? data === null || data === void 0 ? void 0 : (_data$delivery_man_de2 = data.delivery_man_delivery_zone) === null || _data$delivery_man_de2 === void 0 ? void 0 : _data$delivery_man_de2.map(item => ({\n    lat: item === null || item === void 0 ? void 0 : item[0],\n    lng: item === null || item === void 0 ? void 0 : item[1]\n  })) : []);\n  const [merge, setMerge] = useState(null);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const onFinish = () => {\n    if (!(data !== null && data !== void 0 && data.id)) {\n      toast.error(t('no.user.id'));\n      return;\n    }\n    if (triangleCoords.length < 3) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n    if (!merge) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n    const body = {\n      user_id: data === null || data === void 0 ? void 0 : data.id,\n      address: triangleCoords.map(item => ({\n        0: item.lat,\n        1: item.lng\n      }))\n    };\n    setLoadingBtn(true);\n    userService.createAndUpdateDeliverymanZone(body).then(() => {\n      dispatch(setRefetch(activeMenu));\n      toast.success(t('successfully.updated'));\n    }).finally(() => {\n      setLoadingBtn(false);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    layout: \"vertical\",\n    form: form,\n    onFinish: onFinish,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 12,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: MapGif,\n          alt: t('map.gif'),\n          style: {\n            object: 'contain'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(DrawingManager, {\n          triangleCoords: triangleCoords,\n          settriangleCoords: setTriangleCoords,\n          setMerge: setMerge\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      htmlType: \"submit\",\n      loading: loadingBtn,\n      className: \"mt-4\",\n      children: t('save')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(DeliverymanZone, \"5q06aB5gb8ptSY0k1ccGlmBR47A=\", false, function () {\n  return [Form.useForm, useTranslation, useDispatch, useSelector];\n});\n_c = DeliverymanZone;\nexport default DeliverymanZone;\nvar _c;\n$RefreshReg$(_c, \"DeliverymanZone\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Col", "Form", "Row", "MapGif", "DrawingManager", "React", "useState", "useEffect", "useRef", "useTranslation", "toast", "userService", "shallowEqual", "useDispatch", "useSelector", "setRefetch", "jsxDEV", "_jsxDEV", "DeliverymanZone", "data", "_s", "_data$delivery_man_de", "_data$delivery_man_de2", "form", "useForm", "t", "dispatch", "isMountedRef", "current", "activeMenu", "state", "menu", "triangleCoords", "setTriangleCoords", "delivery_man_delivery_zone", "length", "map", "item", "lat", "lng", "merge", "setMerge", "loadingBtn", "setLoadingBtn", "onFinish", "id", "error", "warning", "body", "user_id", "address", "createAndUpdateDeliverymanZone", "then", "success", "finally", "layout", "children", "gutter", "span", "src", "alt", "style", "object", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "settriangleCoords", "type", "htmlType", "loading", "className", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/seller-views/users/components/form/edit/components/deliveryman-zone.js"], "sourcesContent": ["import { But<PERSON>, Col, Form, Row } from 'antd';\nimport MapGif from 'assets/video/map.gif';\nimport DrawingManager from 'components/drawing-map';\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { toast } from 'react-toastify';\nimport userService from 'services/seller/user';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { setRefetch } from 'redux/slices/menu';\n\nconst DeliverymanZone = ({ data }) => {\n  const [form] = Form.useForm();\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const isMountedRef = useRef(true);\n\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n\n  const [triangleCoords, setTriangleCoords] = useState(\n    data?.delivery_man_delivery_zone?.length\n      ? data?.delivery_man_delivery_zone?.map((item) => ({\n          lat: item?.[0],\n          lng: item?.[1],\n        }))\n      : [],\n  );\n  const [merge, setMerge] = useState(null);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n\n  const onFinish = () => {\n    if (!data?.id) {\n      toast.error(t('no.user.id'));\n      return;\n    }\n    if (triangleCoords.length < 3) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n    if (!merge) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n    const body = {\n      user_id: data?.id,\n      address: triangleCoords.map((item) => ({\n        0: item.lat,\n        1: item.lng,\n      })),\n    };\n    setLoadingBtn(true);\n    userService\n      .createAndUpdateDeliverymanZone(body)\n      .then(() => {\n        dispatch(setRefetch(activeMenu));\n        toast.success(t('successfully.updated'));\n      })\n      .finally(() => {\n        setLoadingBtn(false);\n      });\n  };\n\n  return (\n    <Form layout='vertical' form={form} onFinish={onFinish}>\n      <Row gutter={12}>\n        <Col span={12}>\n          <img src={MapGif} alt={t('map.gif')} style={{ object: 'contain' }} />\n        </Col>\n        <Col span={24}>\n          <DrawingManager\n            triangleCoords={triangleCoords}\n            settriangleCoords={setTriangleCoords}\n            setMerge={setMerge}\n          />\n        </Col>\n      </Row>\n      <Button\n        type='primary'\n        htmlType='submit'\n        loading={loadingBtn}\n        className='mt-4'\n      >\n        {t('save')}\n      </Button>\n    </Form>\n  );\n};\n\nexport default DeliverymanZone;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,MAAM;AAC7C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACpC,MAAM,CAACC,IAAI,CAAC,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC;EAC7B,MAAM;IAAEC;EAAE,CAAC,GAAGhB,cAAc,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,YAAY,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXoB,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEC;EAAW,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEnB,YAAY,CAAC;EAEvE,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAClDa,IAAI,aAAJA,IAAI,gBAAAE,qBAAA,GAAJF,IAAI,CAAEe,0BAA0B,cAAAb,qBAAA,eAAhCA,qBAAA,CAAkCc,MAAM,GACpChB,IAAI,aAAJA,IAAI,wBAAAG,sBAAA,GAAJH,IAAI,CAAEe,0BAA0B,cAAAZ,sBAAA,uBAAhCA,sBAAA,CAAkCc,GAAG,CAAEC,IAAI,KAAM;IAC/CC,GAAG,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,CAAC,CAAC;IACdE,GAAG,EAAEF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,CAAC;EACf,CAAC,CAAC,CAAC,GACH,EACN,CAAC;EACD,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMsC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI,EAACzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,EAAE,GAAE;MACbnC,KAAK,CAACoC,KAAK,CAACrB,CAAC,CAAC,YAAY,CAAC,CAAC;MAC5B;IACF;IACA,IAAIO,cAAc,CAACG,MAAM,GAAG,CAAC,EAAE;MAC7BzB,KAAK,CAACqC,OAAO,CAACtB,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACtC;IACF;IACA,IAAI,CAACe,KAAK,EAAE;MACV9B,KAAK,CAACqC,OAAO,CAACtB,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACtC;IACF;IACA,MAAMuB,IAAI,GAAG;MACXC,OAAO,EAAE9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,EAAE;MACjBK,OAAO,EAAElB,cAAc,CAACI,GAAG,CAAEC,IAAI,KAAM;QACrC,CAAC,EAAEA,IAAI,CAACC,GAAG;QACX,CAAC,EAAED,IAAI,CAACE;MACV,CAAC,CAAC;IACJ,CAAC;IACDI,aAAa,CAAC,IAAI,CAAC;IACnBhC,WAAW,CACRwC,8BAA8B,CAACH,IAAI,CAAC,CACpCI,IAAI,CAAC,MAAM;MACV1B,QAAQ,CAACX,UAAU,CAACc,UAAU,CAAC,CAAC;MAChCnB,KAAK,CAAC2C,OAAO,CAAC5B,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC,CAAC,CACD6B,OAAO,CAAC,MAAM;MACbX,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC;EACN,CAAC;EAED,oBACE1B,OAAA,CAAChB,IAAI;IAACsD,MAAM,EAAC,UAAU;IAAChC,IAAI,EAAEA,IAAK;IAACqB,QAAQ,EAAEA,QAAS;IAAAY,QAAA,gBACrDvC,OAAA,CAACf,GAAG;MAACuD,MAAM,EAAE,EAAG;MAAAD,QAAA,gBACdvC,OAAA,CAACjB,GAAG;QAAC0D,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZvC,OAAA;UAAK0C,GAAG,EAAExD,MAAO;UAACyD,GAAG,EAAEnC,CAAC,CAAC,SAAS,CAAE;UAACoC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNjD,OAAA,CAACjB,GAAG;QAAC0D,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZvC,OAAA,CAACb,cAAc;UACb4B,cAAc,EAAEA,cAAe;UAC/BmC,iBAAiB,EAAElC,iBAAkB;UACrCQ,QAAQ,EAAEA;QAAS;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNjD,OAAA,CAAClB,MAAM;MACLqE,IAAI,EAAC,SAAS;MACdC,QAAQ,EAAC,QAAQ;MACjBC,OAAO,EAAE5B,UAAW;MACpB6B,SAAS,EAAC,MAAM;MAAAf,QAAA,EAEf/B,CAAC,CAAC,MAAM;IAAC;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAAC9C,EAAA,CAjFIF,eAAe;EAAA,QACJjB,IAAI,CAACuB,OAAO,EACbf,cAAc,EACXI,WAAW,EASLC,WAAW;AAAA;AAAA0D,EAAA,GAZ9BtD,eAAe;AAmFrB,eAAeA,eAAe;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}