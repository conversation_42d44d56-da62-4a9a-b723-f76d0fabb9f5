9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:23:"App\Models\DeliveryZone":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"shop_delivery_zone";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:7:"shop_id";i:503;s:7:"address";s:245:"[[-18.426849001144433,-50.44582595678896],[-18.44408075633771,-50.48702468725771],[-18.474692629155598,-50.47466506811708],[-18.48706609128848,-50.42556991430849],[-18.448640317388037,-50.41630019995302],[-18.437566887774288,-50.42488326880068]]";s:10:"created_at";s:19:"2025-07-16 22:25:33";s:10:"updated_at";s:19:"2025-07-16 22:25:33";s:10:"deleted_at";N;}s:11:" * original";a:6:{s:2:"id";i:2;s:7:"shop_id";i:503;s:7:"address";s:245:"[[-18.426849001144433,-50.44582595678896],[-18.44408075633771,-50.48702468725771],[-18.474692629155598,-50.47466506811708],[-18.48706609128848,-50.42556991430849],[-18.448640317388037,-50.41630019995302],[-18.437566887774288,-50.42488326880068]]";s:10:"created_at";s:19:"2025-07-16 22:25:33";s:10:"updated_at";s:19:"2025-07-16 22:25:33";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:7:"address";s:5:"array";s:10:"created_at";s:20:"datetime:Y-m-d H:i:s";s:10:"updated_at";s:20:"datetime:Y-m-d H:i:s";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}