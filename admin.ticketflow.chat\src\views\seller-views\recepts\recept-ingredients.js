import { Button, Col, Form, Row, Space } from 'antd';
import React from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { useTranslation } from 'react-i18next';

const ReceptIngredients = ({ next, prev }) => {
  const { t } = useTranslation();
  const form = Form.useFormInstance();
  const { defaultLang, languages } = useSelector(
    (state) => state.formLang,
    shallowEqual
  );
  return (
    <>
      <Row gutter={12}>
        <Col span={24}>
          {languages.map((item) => (
            <Form.Item
              key={`ingredient-${item.locale}`}
              label={t('ingredients')}
              name={['ingredient', item.locale]}
              valuePropName='data'
              getValueFromEvent={(event, editor) => {
                const data = editor.getData();
                return data;
              }}
              rules={[
                {
                  required: item.locale === defaultLang,
                  message: t('required'),
                },
              ]}
              hidden={item.locale !== defaultLang}
            >
              <CKEditor
                editor={ClassicEditor}
                data={form.getFieldValue(['ingredient', item.locale]) || ''}
                onReady={(editor) => {
                  // Editor is ready to use
                }}
                onError={(error, { willEditorRestart }) => {
                  if (willEditorRestart) {
                    editor.ui.view.toolbar.element.style.display = 'none';
                  }
                }}
              />
            </Form.Item>
          ))}
        </Col>
      </Row>
      <Space>
        <Button type='primary' htmlType='button' onClick={() => prev()}>
          {t('prev')}
        </Button>
        <Button
          type='primary'
          htmlType='button'
          onClick={() => {
            form.validateFields([['ingredient', defaultLang]]).then(() => {
              next();
            });
          }}
        >
          {t('next')}
        </Button>
      </Space>
    </>
  );
};

export default ReceptIngredients;
