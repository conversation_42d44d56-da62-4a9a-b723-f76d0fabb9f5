import React, { createContext, useContext, useEffect, useState } from 'react';
import { GoogleApiWrapper } from 'google-maps-react';
import getMapApiKey from 'helpers/getMapApiKey';

const GoogleMapsContext = createContext(null);

// Singleton para evitar múltiplos carregamentos da API
let googleMapsInstance = null;
let isLoading = false;
let loadPromise = null;

const GoogleMapsProvider = ({ children, google }) => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (google && !googleMapsInstance) {
      googleMapsInstance = google;
      setIsReady(true);
    } else if (googleMapsInstance) {
      setIsReady(true);
    }
  }, [google]);

  return (
    <GoogleMapsContext.Provider value={{ google: googleMapsInstance, isReady }}>
      {children}
    </GoogleMapsContext.Provider>
  );
};

export const useGoogleMaps = () => {
  const context = useContext(GoogleMapsContext);
  if (!context) {
    throw new Error('useGoogleMaps must be used within a GoogleMapsProvider');
  }
  return context;
};

// Wrapper com API key
const WrappedGoogleMapsProvider = GoogleApiWrapper({
  apiKey: getMapApiKey(),
  libraries: ['places'],
  LoadingContainer: () => <div>Loading Maps...</div>,
})(GoogleMapsProvider);

export default WrappedGoogleMapsProvider;
