{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\report-products\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Card, Col, Row, Space, Typography, Table, Tag, Button, DatePicker, Spin } from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport SearchInput from '../../components/search-input';\nimport { CloudDownloadOutlined } from '@ant-design/icons';\nimport ReportService from '../../services/reports';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport ReportChart from '../../components/report/chart';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport FilterColumns from '../../components/filter-column';\nimport { export_url } from '../../configs/app-global';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport { clearCompare, fetchReportProduct, fetchReportProductChart, ReportProductCompare } from '../../redux/slices/report/products';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { t } from 'i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport { useMemo } from 'react';\nimport shopService from '../../services/shop';\nimport { DebounceSelect } from 'components/search';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text,\n  Title\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst ReportProducts = () => {\n  _s();\n  var _productList$data, _productList$data2, _productList$data3;\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const [shopId, setShopId] = useState();\n  const [perPageM, setPerPageM] = useState(10);\n  const {\n    date_from,\n    date_to,\n    by_time,\n    chart,\n    handleChart,\n    handleDateRange\n  } = useContext(ReportContext);\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const {\n    loading,\n    chartData: reportData,\n    productList\n  } = useSelector(state => state.productReport, shallowEqual);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [downloading, setDownloading] = useState(false);\n  const [search, setSearch] = useState('');\n  const {\n    defaultCurrency\n  } = useSelector(state => state.currency, shallowEqual);\n  const expandedRowRender = row => {\n    const columns = [{\n      title: t('extras.name'),\n      dataIndex: 'Extras name',\n      render: (_, data) => {\n        var _data$extras;\n        return (_data$extras = data.extras) === null || _data$extras === void 0 ? void 0 : _data$extras.map(extra => {\n          var _extra$group$translat;\n          return (_extra$group$translat = extra.group.translation) === null || _extra$group$translat === void 0 ? void 0 : _extra$group$translat.title;\n        }).join(',');\n      },\n      key: 'Extras name'\n    }, {\n      title: t('item.sold'),\n      dataIndex: 'order_quantity',\n      key: 'order_quantity'\n    }, {\n      title: t('net.sales'),\n      dataIndex: 'upgradeNum',\n      render: (_, data) => numberToPrice(data.price, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position),\n      key: 'upgradeNum'\n    }, {\n      title: t('orders'),\n      dataIndex: 'count',\n      key: 'count'\n    }, {\n      title: t('stock'),\n      dataIndex: 'quantity',\n      key: 'quantity'\n    }];\n    return /*#__PURE__*/_jsxDEV(Table, {\n      scroll: {\n        x: true\n      },\n      columns: columns,\n      dataSource: row.stocks,\n      pagination: false,\n      showHeader: false,\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this);\n  };\n  const [columns, setColumns] = useState([{\n    title: t('product.title'),\n    dataIndex: 'translation_title',\n    key: 'translation_title',\n    render: (_, data) => {\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: `/report/products?product_id=${data.id}`,\n        children: data === null || data === void 0 ? void 0 : data.translation.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this);\n    },\n    is_show: true,\n    sorter: (a, b) => {\n      var _a$translation, _b$translation;\n      return a === null || a === void 0 ? void 0 : (_a$translation = a.translation) === null || _a$translation === void 0 ? void 0 : _a$translation.title.localeCompare(b === null || b === void 0 ? void 0 : (_b$translation = b.translation) === null || _b$translation === void 0 ? void 0 : _b$translation.title);\n    }\n  }, {\n    title: t('bar.code'),\n    dataIndex: 'bar_code',\n    key: 'bar_code',\n    is_show: true,\n    render: (_, data) => {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (data === null || data === void 0 ? void 0 : data.bar_code) || '-'\n      }, void 0, false);\n    }\n  }, {\n    title: t('item.sold'),\n    dataIndex: 'quantity',\n    key: 'quantity',\n    sorter: (a, b) => a.quantity - b.quantity,\n    is_show: true,\n    render: (_, data) => {\n      var _data$stocks;\n      const itemSold = data === null || data === void 0 ? void 0 : (_data$stocks = data.stocks) === null || _data$stocks === void 0 ? void 0 : _data$stocks.map(item => item.order_quantity).reduce((a, b) => a + b, 0);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: itemSold\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: t('net.sales'),\n    dataIndex: 'price',\n    key: 'price',\n    is_show: true,\n    render: price => numberToPrice(price, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position),\n    sorter: (a, b) => a.price - b.price\n  }, {\n    title: t('orders'),\n    key: 'count',\n    dataIndex: 'count',\n    is_show: true,\n    sorter: (a, b) => a.count - b.count\n  }, {\n    title: t('category'),\n    key: 'category',\n    dataIndex: 'category',\n    is_show: true,\n    render: (_, row) => {\n      var _row$category, _row$category2, _row$category2$transl;\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: `/report/products?category_id=${(_row$category = row.category) === null || _row$category === void 0 ? void 0 : _row$category.id}`,\n        children: row === null || row === void 0 ? void 0 : (_row$category2 = row.category) === null || _row$category2 === void 0 ? void 0 : (_row$category2$transl = _row$category2.translation) === null || _row$category2$transl === void 0 ? void 0 : _row$category2$transl.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: t('status'),\n    key: 'active',\n    dataIndex: 'active',\n    render: (_, data) => {\n      const status = Boolean(data === null || data === void 0 ? void 0 : data.active);\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: status ? 'green' : 'red',\n        children: status ? t('active') : t('inactive')\n      }, data.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this);\n    },\n    is_show: true\n  }]);\n  const chart_type = useMemo(() => [{\n    label: 'item.sold',\n    value: 'quantity',\n    qty: 'quantity',\n    price: false\n  }, {\n    label: 'net.sales',\n    value: 'price',\n    qty: 'price',\n    price: true\n  }, {\n    label: t('orders'),\n    value: 'count',\n    qty: 'count',\n    price: false\n  }], []);\n  const fetchReport = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (chart_type.find(item => item.value === chart)) {\n      dispatch(fetchReportProductChart(params));\n    }\n  };\n  const fetchShops = search => {\n    const params = {\n      perPage: 10,\n      search\n    };\n    return shopService.selectPaginate(params).then(res => res === null || res === void 0 ? void 0 : res.map(shop => {\n      var _shop$translation;\n      return {\n        label: shop === null || shop === void 0 ? void 0 : (_shop$translation = shop.translation) === null || _shop$translation === void 0 ? void 0 : _shop$translation.title,\n        value: shop === null || shop === void 0 ? void 0 : shop.id\n      };\n    })).catch(err => console.log('report product ERROR => ', err));\n  };\n  const fetchProduct = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n      search: search || null\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (shopId) params.shop_id = shopId;\n    dispatch(fetchReportProduct(params));\n  };\n  useEffect(() => {\n    handleChart(chart_type[0].value);\n  }, []);\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchProduct();\n      fetchReport();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n  useDidUpdate(() => {\n    fetchProduct();\n  }, [date_to, search, category_id, product_id, by_time, date_from, shopId]);\n  useDidUpdate(() => {\n    fetchReport();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n  const onChangePagination = pagination => {\n    const {\n      pageSize: perPage,\n      current: page\n    } = pagination;\n    setPerPageM(perPage);\n    fetchProduct(page, perPage);\n  };\n  const excelExport = () => {\n    setDownloading(true);\n    ReportService.getReportProductList({\n      date_from,\n      date_to,\n      type: by_time,\n      export: 'excel',\n      shop_id: shopId,\n      products: rowSelection !== null && rowSelection !== void 0 && rowSelection.selectedRowKeys[0] ? rowSelection === null || rowSelection === void 0 ? void 0 : rowSelection.selectedRowKeys : product_id ? [product_id] : undefined\n    }).then(res => {\n      const body = export_url + res.data.file_name;\n      window.location.href = body;\n    }).finally(() => setDownloading(false));\n  };\n  const onSelectChange = newSelectedRowKeys => {\n    setSelectedRowKeys(newSelectedRowKeys);\n  };\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: onSelectChange\n  };\n  const Compare = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart,\n      ids: selectedRowKeys,\n      shop_id: shopId\n    };\n    dispatch(ReportProductCompare(params));\n  };\n  const clear = () => {\n    dispatch(clearCompare());\n    setShopId(undefined);\n    setSelectedRowKeys([]);\n    fetchProduct();\n    fetchReport();\n    navigate(`/report/products`);\n  };\n  const onShopSelectClear = () => {\n    setShopId(undefined);\n    fetchReportProduct();\n    fetchReportProductChart({});\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    size: \"large\",\n    spinning: loading,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            ...configureRangePicker(),\n            defaultValue: [moment(date_from), moment(date_to)],\n            onChange: handleDateRange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"report-products\",\n      children: chart_type === null || chart_type === void 0 ? void 0 : chart_type.map(item => /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        onClick: () => handleChart(item.value),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: chart === item.value && 'active',\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-5\",\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                children: t(item.label)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 24,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Title, {\n                level: 2,\n                children: !(item !== null && item !== void 0 && item.price) ? reportData[item.qty] : numberToPrice(reportData[item.qty], defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this)\n      }, item.label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ReportChart, {\n      reportData: reportData,\n      chart_data: \"quantities_sum\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        className: \"d-flex justify-content-between align-center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n          strong: true,\n          level: 3,\n          children: t('products')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          className: \"d-flex justify-content-between\",\n          children: [/*#__PURE__*/_jsxDEV(SearchInput, {\n            style: {\n              minWidth: '300px'\n            },\n            handleChange: e => setSearch(e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DebounceSelect, {\n            fetchOptions: fetchShops,\n            placeholder: t('select.shop'),\n            onSelect: value => setShopId(value.value),\n            onClear: () => onShopSelectClear()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: Boolean(selectedRowKeys.length) || !!category_id || !!product_id || !!shopId ? 'primary' : 'default',\n            danger: Boolean(selectedRowKeys.length) || !!category_id || !!product_id || !!shopId,\n            onClick: clear,\n            children: t('clear')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(CloudDownloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 21\n            }, this),\n            loading: downloading,\n            onClick: excelExport,\n            children: t('download')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FilterColumns, {\n            columns: columns,\n            setColumns: setColumns\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        expandable: {\n          expandedRowRender,\n          defaultExpandedRowKeys: ['0']\n        },\n        rowSelection: rowSelection,\n        columns: columns === null || columns === void 0 ? void 0 : columns.filter(item => item.is_show),\n        dataSource: (_productList$data = productList.data) === null || _productList$data === void 0 ? void 0 : _productList$data.data,\n        rowKey: row => row.id,\n        loading: loading,\n        pagination: {\n          pageSize: perPageM,\n          page: (productList === null || productList === void 0 ? void 0 : (_productList$data2 = productList.data) === null || _productList$data2 === void 0 ? void 0 : _productList$data2.meta.page) || 1,\n          total: productList === null || productList === void 0 ? void 0 : (_productList$data3 = productList.data) === null || _productList$data3 === void 0 ? void 0 : _productList$data3.meta.total,\n          defaultCurrent: 1\n        },\n        onChange: onChangePagination,\n        scroll: {\n          x: 1500\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 348,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportProducts, \"VNVH1wVd6fM8XKPdvvJegw2kAdY=\", false, function () {\n  return [useDispatch, useLocation, useNavigate, useSelector, useSelector, useSelector, useDidUpdate, useDidUpdate];\n});\n_c = ReportProducts;\nexport default ReportProducts;\nvar _c;\n$RefreshReg$(_c, \"ReportProducts\");", "map": {"version": 3, "names": ["Card", "Col", "Row", "Space", "Typography", "Table", "Tag", "<PERSON><PERSON>", "DatePicker", "Spin", "React", "useContext", "useEffect", "useState", "SearchInput", "CloudDownloadOutlined", "ReportService", "disable<PERSON><PERSON><PERSON><PERSON>", "shallowEqual", "useDispatch", "useSelector", "ReportChart", "moment", "ReportContext", "FilterColumns", "export_url", "configureRangePicker", "clearCompare", "fetchReportProduct", "fetchReportProductChart", "ReportProductCompare", "useDidUpdate", "Link", "useLocation", "useNavigate", "QueryString", "t", "numberToPrice", "useMemo", "shopService", "DebounceSelect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "Title", "RangePicker", "ReportProducts", "_s", "_productList$data", "_productList$data2", "_productList$data3", "dispatch", "location", "navigate", "category_id", "parse", "search", "product_id", "shopId", "setShopId", "perPageM", "setPerPageM", "date_from", "date_to", "by_time", "chart", "handleChart", "handleDateRange", "activeMenu", "state", "menu", "loading", "chartData", "reportData", "productList", "productReport", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "downloading", "setDownloading", "setSearch", "defaultCurrency", "currency", "expandedRowRender", "row", "columns", "title", "dataIndex", "render", "_", "data", "_data$extras", "extras", "map", "extra", "_extra$group$translat", "group", "translation", "join", "key", "price", "symbol", "position", "scroll", "x", "dataSource", "stocks", "pagination", "showHeader", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "setColumns", "to", "id", "children", "is_show", "sorter", "a", "b", "_a$translation", "_b$translation", "localeCompare", "bar_code", "quantity", "_data$stocks", "itemSold", "item", "order_quantity", "reduce", "count", "_row$category", "_row$category2", "_row$category2$transl", "category", "status", "Boolean", "active", "color", "chart_type", "label", "value", "qty", "fetchReport", "params", "type", "categories", "products", "find", "fetchShops", "perPage", "selectPaginate", "then", "res", "shop", "_shop$translation", "catch", "err", "console", "log", "fetchProduct", "page", "shop_id", "refetch", "onChangePagination", "pageSize", "current", "excelExport", "getReportProductList", "export", "rowSelection", "undefined", "body", "file_name", "window", "href", "finally", "onSelectChange", "newSelectedRowKeys", "onChange", "Compare", "ids", "clear", "onShopSelectClear", "spinning", "gutter", "className", "span", "defaultValue", "onClick", "level", "chart_data", "strong", "style", "min<PERSON><PERSON><PERSON>", "handleChange", "e", "fetchOptions", "placeholder", "onSelect", "onClear", "length", "danger", "icon", "expandable", "defaultExpandedRowKeys", "filter", "<PERSON><PERSON><PERSON>", "meta", "total", "defaultCurrent", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/report-products/index.js"], "sourcesContent": ["import {\n  <PERSON>,\n  Col,\n  Row,\n  Space,\n  Typography,\n  Table,\n  Tag,\n  <PERSON><PERSON>,\n  DatePicker,\n  Spin,\n} from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport SearchInput from '../../components/search-input';\nimport { CloudDownloadOutlined } from '@ant-design/icons';\nimport ReportService from '../../services/reports';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport ReportChart from '../../components/report/chart';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport FilterColumns from '../../components/filter-column';\nimport { export_url } from '../../configs/app-global';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport {\n  clearCompare,\n  fetchReportProduct,\n  fetchReportProduct<PERSON>hart,\n  ReportProductCompare,\n} from '../../redux/slices/report/products';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { t } from 'i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport { useMemo } from 'react';\nimport shopService from '../../services/shop';\nimport { DebounceSelect } from 'components/search';\nconst { Text, Title } = Typography;\nconst { RangePicker } = DatePicker;\n\nconst ReportProducts = () => {\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const [shopId, setShopId] = useState();\n  const [perPageM, setPerPageM] = useState(10);\n  const { date_from, date_to, by_time, chart, handleChart, handleDateRange } =\n    useContext(ReportContext);\n\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n\n  const {\n    loading,\n    chartData: reportData,\n    productList,\n  } = useSelector((state) => state.productReport, shallowEqual);\n\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [downloading, setDownloading] = useState(false);\n  const [search, setSearch] = useState('');\n  const { defaultCurrency } = useSelector(\n    (state) => state.currency,\n    shallowEqual,\n  );\n\n  const expandedRowRender = (row) => {\n    const columns = [\n      {\n        title: t('extras.name'),\n        dataIndex: 'Extras name',\n        render: (_, data) =>\n          data.extras?.map((extra) => extra.group.translation?.title).join(','),\n        key: 'Extras name',\n      },\n      {\n        title: t('item.sold'),\n        dataIndex: 'order_quantity',\n        key: 'order_quantity',\n      },\n      {\n        title: t('net.sales'),\n        dataIndex: 'upgradeNum',\n        render: (_, data) =>\n          numberToPrice(\n            data.price,\n            defaultCurrency?.symbol,\n            defaultCurrency?.position,\n          ),\n        key: 'upgradeNum',\n      },\n      {\n        title: t('orders'),\n        dataIndex: 'count',\n        key: 'count',\n      },\n      {\n        title: t('stock'),\n        dataIndex: 'quantity',\n        key: 'quantity',\n      },\n    ];\n    return (\n      <Table\n        scroll={{ x: true }}\n        columns={columns}\n        dataSource={row.stocks}\n        pagination={false}\n        showHeader={false}\n        size=\"small\"\n      />\n    );\n  };\n\n  const [columns, setColumns] = useState([\n    {\n      title: t('product.title'),\n      dataIndex: 'translation_title',\n      key: 'translation_title',\n      render: (_, data) => {\n        return (\n          <Link to={`/report/products?product_id=${data.id}`}>\n            {data?.translation.title}\n          </Link>\n        );\n      },\n      is_show: true,\n      sorter: (a, b) =>\n        a?.translation?.title.localeCompare(b?.translation?.title),\n    },\n    {\n      title: t('bar.code'),\n      dataIndex: 'bar_code',\n      key: 'bar_code',\n      is_show: true,\n      render: (_, data) => {\n        return <>{data?.bar_code || '-'}</>;\n      },\n    },\n    {\n      title: t('item.sold'),\n      dataIndex: 'quantity',\n      key: 'quantity',\n      sorter: (a, b) => a.quantity - b.quantity,\n      is_show: true,\n      render: (_, data) => {\n        const itemSold = data?.stocks\n          ?.map((item) => item.order_quantity)\n          .reduce((a, b) => a + b, 0);\n        return <div>{itemSold}</div>;\n      },\n    },\n    {\n      title: t('net.sales'),\n      dataIndex: 'price',\n      key: 'price',\n      is_show: true,\n      render: (price) =>\n        numberToPrice(\n          price,\n          defaultCurrency?.symbol,\n          defaultCurrency?.position,\n        ),\n      sorter: (a, b) => a.price - b.price,\n    },\n    {\n      title: t('orders'),\n      key: 'count',\n      dataIndex: 'count',\n      is_show: true,\n      sorter: (a, b) => a.count - b.count,\n    },\n    {\n      title: t('category'),\n      key: 'category',\n      dataIndex: 'category',\n      is_show: true,\n      render: (_, row) => {\n        return (\n          <Link to={`/report/products?category_id=${row.category?.id}`}>\n            {row?.category?.translation?.title}\n          </Link>\n        );\n      },\n    },\n    {\n      title: t('status'),\n      key: 'active',\n      dataIndex: 'active',\n      render: (_, data) => {\n        const status = Boolean(data?.active);\n        return (\n          <Tag color={status ? 'green' : 'red'} key={data.id}>\n            {status ? t('active') : t('inactive')}\n          </Tag>\n        );\n      },\n      is_show: true,\n    },\n  ]);\n\n  const chart_type = useMemo(\n    () => [\n      {\n        label: 'item.sold',\n        value: 'quantity',\n        qty: 'quantity',\n        price: false,\n      },\n      { label: 'net.sales', value: 'price', qty: 'price', price: true },\n      { label: t('orders'), value: 'count', qty: 'count', price: false },\n    ],\n    [],\n  );\n\n  const fetchReport = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart,\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (chart_type.find((item) => item.value === chart)) {\n      dispatch(fetchReportProductChart(params));\n    }\n  };\n\n  const fetchShops = (search) => {\n    const params = {\n      perPage: 10,\n      search,\n    };\n    return shopService\n      .selectPaginate(params)\n      .then((res) =>\n        res?.map((shop) => ({\n          label: shop?.translation?.title,\n          value: shop?.id,\n        })),\n      )\n      .catch((err) => console.log('report product ERROR => ', err));\n  };\n\n  const fetchProduct = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n      search: search || null,\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (shopId) params.shop_id = shopId;\n    dispatch(fetchReportProduct(params));\n  };\n\n  useEffect(() => {\n    handleChart(chart_type[0].value);\n  }, []);\n\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchProduct();\n      fetchReport();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n\n  useDidUpdate(() => {\n    fetchProduct();\n  }, [date_to, search, category_id, product_id, by_time, date_from, shopId]);\n\n  useDidUpdate(() => {\n    fetchReport();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n\n  const onChangePagination = (pagination) => {\n    const { pageSize: perPage, current: page } = pagination;\n    setPerPageM(perPage);\n    fetchProduct(page, perPage);\n  };\n\n  const excelExport = () => {\n    setDownloading(true);\n    ReportService.getReportProductList({\n      date_from,\n      date_to,\n      type: by_time,\n      export: 'excel',\n      shop_id: shopId,\n      products: rowSelection?.selectedRowKeys[0]\n        ? rowSelection?.selectedRowKeys\n        : product_id\n        ? [product_id]\n        : undefined,\n    })\n      .then((res) => {\n        const body = export_url + res.data.file_name;\n        window.location.href = body;\n      })\n      .finally(() => setDownloading(false));\n  };\n\n  const onSelectChange = (newSelectedRowKeys) => {\n    setSelectedRowKeys(newSelectedRowKeys);\n  };\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: onSelectChange,\n  };\n\n  const Compare = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart,\n      ids: selectedRowKeys,\n      shop_id: shopId,\n    };\n\n    dispatch(ReportProductCompare(params));\n  };\n\n  const clear = () => {\n    dispatch(clearCompare());\n    setShopId(undefined);\n    setSelectedRowKeys([]);\n    fetchProduct();\n    fetchReport();\n    navigate(`/report/products`);\n  };\n\n  const onShopSelectClear = () => {\n    setShopId(undefined);\n    fetchReportProduct();\n    fetchReportProductChart({});\n  };\n\n  return (\n    <Spin size='large' spinning={loading}>\n      <Row gutter={24} className='mb-3'>\n        <Col span={12}>\n          <Space>\n            <RangePicker\n              {...configureRangePicker()}\n              defaultValue={[moment(date_from), moment(date_to)]}\n              onChange={handleDateRange}\n            />\n          </Space>\n        </Col>\n      </Row>\n      <Row gutter={24} className='report-products'>\n        {chart_type?.map((item) => (\n          <Col\n            span={8}\n            key={item.label}\n            onClick={() => handleChart(item.value)}\n          >\n            <Card className={chart === item.value && 'active'}>\n              <Row className='mb-5'>\n                <Col>\n                  <Text>{t(item.label)}</Text>\n                </Col>\n              </Row>\n              <Row gutter={24}>\n                <Col span={12}>\n                  <Title level={2}>\n                    {!item?.price\n                      ? reportData[item.qty]\n                      : numberToPrice(\n                          reportData[item.qty],\n                          defaultCurrency?.symbol,\n                          defaultCurrency?.position,\n                        )}\n                  </Title>\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n      <ReportChart reportData={reportData} chart_data='quantities_sum' />\n      <Card>\n        <Space className='d-flex justify-content-between align-center'>\n          <Typography.Text strong level={3}>\n            {t('products')}\n          </Typography.Text>\n          <Space className='d-flex justify-content-between'>\n            <SearchInput\n              style={{ minWidth: '300px' }}\n              handleChange={(e) => setSearch(e)}\n            />\n            <DebounceSelect\n              fetchOptions={fetchShops}\n              placeholder={t('select.shop')}\n              onSelect={(value) => setShopId(value.value)}\n              onClear={() => onShopSelectClear()}\n            />\n            <Button\n              type={\n                Boolean(selectedRowKeys.length) ||\n                !!category_id ||\n                !!product_id ||\n                !!shopId\n                  ? 'primary'\n                  : 'default'\n              }\n              danger={\n                Boolean(selectedRowKeys.length) ||\n                !!category_id ||\n                !!product_id ||\n                !!shopId\n              }\n              onClick={clear}\n            >\n              {t('clear')}\n            </Button>\n            <Button\n              icon={<CloudDownloadOutlined />}\n              loading={downloading}\n              onClick={excelExport}\n            >\n              {t('download')}\n            </Button>\n            <FilterColumns columns={columns} setColumns={setColumns} />\n          </Space>\n        </Space>\n\n        <Table\n          expandable={{\n            expandedRowRender,\n            defaultExpandedRowKeys: ['0'],\n          }}\n          rowSelection={rowSelection}\n          columns={columns?.filter((item) => item.is_show)}\n          dataSource={productList.data?.data}\n          rowKey={(row) => row.id}\n          loading={loading}\n          pagination={{\n            pageSize: perPageM,\n            page: productList?.data?.meta.page || 1,\n            total: productList?.data?.meta.total,\n            defaultCurrent: 1,\n          }}\n          onChange={onChangePagination}\n          scroll={{\n            x: 1500,\n          }}\n        />\n      </Card>\n    </Spin>\n  );\n};\n\nexport default ReportProducts;\n"], "mappings": ";;AAAA,SACEA,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,IAAI,QACC,MAAM;AACb,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,OAAOC,WAAW,MAAM,+BAA+B;AACvD,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SACEC,YAAY,EACZC,kBAAkB,EAClBC,uBAAuB,EACvBC,oBAAoB,QACf,oCAAoC;AAC3C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,WAAW,MAAM,IAAI;AAC5B,SAASC,CAAC,QAAQ,SAAS;AAC3B,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACnD,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAG1C,UAAU;AAClC,MAAM;EAAE2C;AAAY,CAAC,GAAGvC,UAAU;AAElC,MAAMwC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;EAC3B,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,WAAW,GAAGrB,WAAW,CAACsB,KAAK,CAACH,QAAQ,CAACI,MAAM,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC;EAC1E,MAAMC,UAAU,GAAGxB,WAAW,CAACsB,KAAK,CAACH,QAAQ,CAACI,MAAM,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC;EACxE,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,CAAC;EACtC,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM;IAAEmD,SAAS;IAAEC,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAgB,CAAC,GACxE1D,UAAU,CAACY,aAAa,CAAC;EAE3B,MAAM;IAAE+C;EAAW,CAAC,GAAGlD,WAAW,CAAEmD,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEtD,YAAY,CAAC;EAEvE,MAAM;IACJuD,OAAO;IACPC,SAAS,EAAEC,UAAU;IACrBC;EACF,CAAC,GAAGxD,WAAW,CAAEmD,KAAK,IAAKA,KAAK,CAACM,aAAa,EAAE3D,YAAY,CAAC;EAE7D,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,MAAM,EAAEwB,SAAS,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM;IAAEsE;EAAgB,CAAC,GAAG/D,WAAW,CACpCmD,KAAK,IAAKA,KAAK,CAACa,QAAQ,EACzBlE,YACF,CAAC;EAED,MAAMmE,iBAAiB,GAAIC,GAAG,IAAK;IACjC,MAAMC,OAAO,GAAG,CACd;MACEC,KAAK,EAAEpD,CAAC,CAAC,aAAa,CAAC;MACvBqD,SAAS,EAAE,aAAa;MACxBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI;QAAA,IAAAC,YAAA;QAAA,QAAAA,YAAA,GACdD,IAAI,CAACE,MAAM,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,GAAG,CAAEC,KAAK;UAAA,IAAAC,qBAAA;UAAA,QAAAA,qBAAA,GAAKD,KAAK,CAACE,KAAK,CAACC,WAAW,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBT,KAAK;QAAA,EAAC,CAACY,IAAI,CAAC,GAAG,CAAC;MAAA;MACvEC,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;MACrBqD,SAAS,EAAE,gBAAgB;MAC3BY,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;MACrBqD,SAAS,EAAE,YAAY;MACvBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KACdvD,aAAa,CACXuD,IAAI,CAACU,KAAK,EACVnB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,MAAM,EACvBpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,QACnB,CAAC;MACHH,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,QAAQ,CAAC;MAClBqD,SAAS,EAAE,OAAO;MAClBY,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,OAAO,CAAC;MACjBqD,SAAS,EAAE,UAAU;MACrBY,GAAG,EAAE;IACP,CAAC,CACF;IACD,oBACE3D,OAAA,CAACrC,KAAK;MACJoG,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK,CAAE;MACpBnB,OAAO,EAAEA,OAAQ;MACjBoB,UAAU,EAAErB,GAAG,CAACsB,MAAO;MACvBC,UAAU,EAAE,KAAM;MAClBC,UAAU,EAAE,KAAM;MAClBC,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEN,CAAC;EAED,MAAM,CAAC5B,OAAO,EAAE6B,UAAU,CAAC,GAAGvG,QAAQ,CAAC,CACrC;IACE2E,KAAK,EAAEpD,CAAC,CAAC,eAAe,CAAC;IACzBqD,SAAS,EAAE,mBAAmB;IAC9BY,GAAG,EAAE,mBAAmB;IACxBX,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,oBACElD,OAAA,CAACV,IAAI;QAACqF,EAAE,EAAG,+BAA8BzB,IAAI,CAAC0B,EAAG,EAAE;QAAAC,QAAA,EAChD3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,WAAW,CAACX;MAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAEX,CAAC;IACDK,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC;MAAA,IAAAC,cAAA,EAAAC,cAAA;MAAA,OACXH,CAAC,aAADA,CAAC,wBAAAE,cAAA,GAADF,CAAC,CAAEvB,WAAW,cAAAyB,cAAA,uBAAdA,cAAA,CAAgBpC,KAAK,CAACsC,aAAa,CAACH,CAAC,aAADA,CAAC,wBAAAE,cAAA,GAADF,CAAC,CAAExB,WAAW,cAAA0B,cAAA,uBAAdA,cAAA,CAAgBrC,KAAK,CAAC;IAAA;EAC9D,CAAC,EACD;IACEA,KAAK,EAAEpD,CAAC,CAAC,UAAU,CAAC;IACpBqD,SAAS,EAAE,UAAU;IACrBY,GAAG,EAAE,UAAU;IACfmB,OAAO,EAAE,IAAI;IACb9B,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,oBAAOlD,OAAA,CAAAE,SAAA;QAAA2E,QAAA,EAAG,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,QAAQ,KAAI;MAAG,gBAAG,CAAC;IACrC;EACF,CAAC,EACD;IACEvC,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;IACrBqD,SAAS,EAAE,UAAU;IACrBY,GAAG,EAAE,UAAU;IACfoB,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACM,QAAQ,GAAGL,CAAC,CAACK,QAAQ;IACzCR,OAAO,EAAE,IAAI;IACb9B,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MAAA,IAAAqC,YAAA;MACnB,MAAMC,QAAQ,GAAGtC,IAAI,aAAJA,IAAI,wBAAAqC,YAAA,GAAJrC,IAAI,CAAEgB,MAAM,cAAAqB,YAAA,uBAAZA,YAAA,CACblC,GAAG,CAAEoC,IAAI,IAAKA,IAAI,CAACC,cAAc,CAAC,CACnCC,MAAM,CAAC,CAACX,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;MAC7B,oBAAOjF,OAAA;QAAA6E,QAAA,EAAMW;MAAQ;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC9B;EACF,CAAC,EACD;IACE3B,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;IACrBqD,SAAS,EAAE,OAAO;IAClBY,GAAG,EAAE,OAAO;IACZmB,OAAO,EAAE,IAAI;IACb9B,MAAM,EAAGY,KAAK,IACZjE,aAAa,CACXiE,KAAK,EACLnB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,MAAM,EACvBpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,QACnB,CAAC;IACHiB,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpB,KAAK,GAAGqB,CAAC,CAACrB;EAChC,CAAC,EACD;IACEd,KAAK,EAAEpD,CAAC,CAAC,QAAQ,CAAC;IAClBiE,GAAG,EAAE,OAAO;IACZZ,SAAS,EAAE,OAAO;IAClB+B,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACY,KAAK,GAAGX,CAAC,CAACW;EAChC,CAAC,EACD;IACE9C,KAAK,EAAEpD,CAAC,CAAC,UAAU,CAAC;IACpBiE,GAAG,EAAE,UAAU;IACfZ,SAAS,EAAE,UAAU;IACrB+B,OAAO,EAAE,IAAI;IACb9B,MAAM,EAAEA,CAACC,CAAC,EAAEL,GAAG,KAAK;MAAA,IAAAiD,aAAA,EAAAC,cAAA,EAAAC,qBAAA;MAClB,oBACE/F,OAAA,CAACV,IAAI;QAACqF,EAAE,EAAG,gCAA6B,CAAAkB,aAAA,GAAEjD,GAAG,CAACoD,QAAQ,cAAAH,aAAA,uBAAZA,aAAA,CAAcjB,EAAG,EAAE;QAAAC,QAAA,EAC1DjC,GAAG,aAAHA,GAAG,wBAAAkD,cAAA,GAAHlD,GAAG,CAAEoD,QAAQ,cAAAF,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAerC,WAAW,cAAAsC,qBAAA,uBAA1BA,qBAAA,CAA4BjD;MAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAEX;EACF,CAAC,EACD;IACE3B,KAAK,EAAEpD,CAAC,CAAC,QAAQ,CAAC;IAClBiE,GAAG,EAAE,QAAQ;IACbZ,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,MAAM+C,MAAM,GAAGC,OAAO,CAAChD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,MAAM,CAAC;MACpC,oBACEnG,OAAA,CAACpC,GAAG;QAACwI,KAAK,EAAEH,MAAM,GAAG,OAAO,GAAG,KAAM;QAAApB,QAAA,EAClCoB,MAAM,GAAGvG,CAAC,CAAC,QAAQ,CAAC,GAAGA,CAAC,CAAC,UAAU;MAAC,GADIwD,IAAI,CAAC0B,EAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7C,CAAC;IAEV,CAAC;IACDK,OAAO,EAAE;EACX,CAAC,CACF,CAAC;EAEF,MAAMuB,UAAU,GAAGzG,OAAO,CACxB,MAAM,CACJ;IACE0G,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,UAAU;IACf5C,KAAK,EAAE;EACT,CAAC,EACD;IAAE0C,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAE5C,KAAK,EAAE;EAAK,CAAC,EACjE;IAAE0C,KAAK,EAAE5G,CAAC,CAAC,QAAQ,CAAC;IAAE6G,KAAK,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAE5C,KAAK,EAAE;EAAM,CAAC,CACnE,EACD,EACF,CAAC;EAED,MAAM6C,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,MAAM,GAAG;MACbpF,SAAS;MACTC,OAAO;MACPoF,IAAI,EAAEnF,OAAO;MACbC;IACF,CAAC;IACD,IAAIX,WAAW,EAAE4F,MAAM,CAACE,UAAU,GAAG,CAAC9F,WAAW,CAAC;IAClD,IAAIG,UAAU,EAAEyF,MAAM,CAACG,QAAQ,GAAG,CAAC5F,UAAU,CAAC;IAC9C,IAAIoF,UAAU,CAACS,IAAI,CAAErB,IAAI,IAAKA,IAAI,CAACc,KAAK,KAAK9E,KAAK,CAAC,EAAE;MACnDd,QAAQ,CAACxB,uBAAuB,CAACuH,MAAM,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMK,UAAU,GAAI/F,MAAM,IAAK;IAC7B,MAAM0F,MAAM,GAAG;MACbM,OAAO,EAAE,EAAE;MACXhG;IACF,CAAC;IACD,OAAOnB,WAAW,CACfoH,cAAc,CAACP,MAAM,CAAC,CACtBQ,IAAI,CAAEC,GAAG,IACRA,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE9D,GAAG,CAAE+D,IAAI;MAAA,IAAAC,iBAAA;MAAA,OAAM;QAClBf,KAAK,EAAEc,IAAI,aAAJA,IAAI,wBAAAC,iBAAA,GAAJD,IAAI,CAAE3D,WAAW,cAAA4D,iBAAA,uBAAjBA,iBAAA,CAAmBvE,KAAK;QAC/ByD,KAAK,EAAEa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAExC;MACf,CAAC;IAAA,CAAC,CACJ,CAAC,CACA0C,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,GAAG,CAAC,CAAC;EACjE,CAAC;EAED,MAAMG,YAAY,GAAGA,CAACC,IAAI,EAAEX,OAAO,KAAK;IACtC,MAAMN,MAAM,GAAG;MACbpF,SAAS;MACTC,OAAO;MACPoF,IAAI,EAAEnF,OAAO;MACbmG,IAAI;MACJX,OAAO;MACPhG,MAAM,EAAEA,MAAM,IAAI;IACpB,CAAC;IACD,IAAIF,WAAW,EAAE4F,MAAM,CAACE,UAAU,GAAG,CAAC9F,WAAW,CAAC;IAClD,IAAIG,UAAU,EAAEyF,MAAM,CAACG,QAAQ,GAAG,CAAC5F,UAAU,CAAC;IAC9C,IAAIC,MAAM,EAAEwF,MAAM,CAACkB,OAAO,GAAG1G,MAAM;IACnCP,QAAQ,CAACzB,kBAAkB,CAACwH,MAAM,CAAC,CAAC;EACtC,CAAC;EAEDxI,SAAS,CAAC,MAAM;IACdwD,WAAW,CAAC2E,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAENrI,SAAS,CAAC,MAAM;IACd,IAAI0D,UAAU,CAACiG,OAAO,EAAE;MACtBH,YAAY,CAAC,CAAC;MACdjB,WAAW,CAAC,CAAC;MACb9F,QAAQ,CAACpC,cAAc,CAACqD,UAAU,CAAC,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,UAAU,CAACiG,OAAO,CAAC,CAAC;EAExBxI,YAAY,CAAC,MAAM;IACjBqI,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACnG,OAAO,EAAEP,MAAM,EAAEF,WAAW,EAAEG,UAAU,EAAEO,OAAO,EAAEF,SAAS,EAAEJ,MAAM,CAAC,CAAC;EAE1E7B,YAAY,CAAC,MAAM;IACjBoH,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAClF,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEX,WAAW,EAAEG,UAAU,EAAEK,SAAS,CAAC,CAAC;EAEjE,MAAMwG,kBAAkB,GAAI3D,UAAU,IAAK;IACzC,MAAM;MAAE4D,QAAQ,EAAEf,OAAO;MAAEgB,OAAO,EAAEL;IAAK,CAAC,GAAGxD,UAAU;IACvD9C,WAAW,CAAC2F,OAAO,CAAC;IACpBU,YAAY,CAACC,IAAI,EAAEX,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxB1F,cAAc,CAAC,IAAI,CAAC;IACpBjE,aAAa,CAAC4J,oBAAoB,CAAC;MACjC5G,SAAS;MACTC,OAAO;MACPoF,IAAI,EAAEnF,OAAO;MACb2G,MAAM,EAAE,OAAO;MACfP,OAAO,EAAE1G,MAAM;MACf2F,QAAQ,EAAEuB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEhG,eAAe,CAAC,CAAC,CAAC,GACtCgG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEhG,eAAe,GAC7BnB,UAAU,GACV,CAACA,UAAU,CAAC,GACZoH;IACN,CAAC,CAAC,CACCnB,IAAI,CAAEC,GAAG,IAAK;MACb,MAAMmB,IAAI,GAAGvJ,UAAU,GAAGoI,GAAG,CAACjE,IAAI,CAACqF,SAAS;MAC5CC,MAAM,CAAC5H,QAAQ,CAAC6H,IAAI,GAAGH,IAAI;IAC7B,CAAC,CAAC,CACDI,OAAO,CAAC,MAAMnG,cAAc,CAAC,KAAK,CAAC,CAAC;EACzC,CAAC;EAED,MAAMoG,cAAc,GAAIC,kBAAkB,IAAK;IAC7CvG,kBAAkB,CAACuG,kBAAkB,CAAC;EACxC,CAAC;EAED,MAAMR,YAAY,GAAG;IACnBhG,eAAe;IACfyG,QAAQ,EAAEF;EACZ,CAAC;EAED,MAAMG,OAAO,GAAGA,CAAA,KAAM;IACpB,MAAMpC,MAAM,GAAG;MACbpF,SAAS;MACTC,OAAO;MACPoF,IAAI,EAAEnF,OAAO;MACbC,KAAK;MACLsH,GAAG,EAAE3G,eAAe;MACpBwF,OAAO,EAAE1G;IACX,CAAC;IAEDP,QAAQ,CAACvB,oBAAoB,CAACsH,MAAM,CAAC,CAAC;EACxC,CAAC;EAED,MAAMsC,KAAK,GAAGA,CAAA,KAAM;IAClBrI,QAAQ,CAAC1B,YAAY,CAAC,CAAC,CAAC;IACxBkC,SAAS,CAACkH,SAAS,CAAC;IACpBhG,kBAAkB,CAAC,EAAE,CAAC;IACtBqF,YAAY,CAAC,CAAC;IACdjB,WAAW,CAAC,CAAC;IACb5F,QAAQ,CAAE,kBAAiB,CAAC;EAC9B,CAAC;EAED,MAAMoI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9H,SAAS,CAACkH,SAAS,CAAC;IACpBnJ,kBAAkB,CAAC,CAAC;IACpBC,uBAAuB,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC;EAED,oBACEa,OAAA,CAACjC,IAAI;IAACsG,IAAI,EAAC,OAAO;IAAC6E,QAAQ,EAAEnH,OAAQ;IAAA8C,QAAA,gBACnC7E,OAAA,CAACxC,GAAG;MAAC2L,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,MAAM;MAAAvE,QAAA,eAC/B7E,OAAA,CAACzC,GAAG;QAAC8L,IAAI,EAAE,EAAG;QAAAxE,QAAA,eACZ7E,OAAA,CAACvC,KAAK;UAAAoH,QAAA,eACJ7E,OAAA,CAACK,WAAW;YAAA,GACNrB,oBAAoB,CAAC,CAAC;YAC1BsK,YAAY,EAAE,CAAC1K,MAAM,CAAC0C,SAAS,CAAC,EAAE1C,MAAM,CAAC2C,OAAO,CAAC,CAAE;YACnDsH,QAAQ,EAAElH;UAAgB;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNzE,OAAA,CAACxC,GAAG;MAAC2L,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,iBAAiB;MAAAvE,QAAA,EACzCwB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEhD,GAAG,CAAEoC,IAAI,iBACpBzF,OAAA,CAACzC,GAAG;QACF8L,IAAI,EAAE,CAAE;QAERE,OAAO,EAAEA,CAAA,KAAM7H,WAAW,CAAC+D,IAAI,CAACc,KAAK,CAAE;QAAA1B,QAAA,eAEvC7E,OAAA,CAAC1C,IAAI;UAAC8L,SAAS,EAAE3H,KAAK,KAAKgE,IAAI,CAACc,KAAK,IAAI,QAAS;UAAA1B,QAAA,gBAChD7E,OAAA,CAACxC,GAAG;YAAC4L,SAAS,EAAC,MAAM;YAAAvE,QAAA,eACnB7E,OAAA,CAACzC,GAAG;cAAAsH,QAAA,eACF7E,OAAA,CAACG,IAAI;gBAAA0E,QAAA,EAAEnF,CAAC,CAAC+F,IAAI,CAACa,KAAK;cAAC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzE,OAAA,CAACxC,GAAG;YAAC2L,MAAM,EAAE,EAAG;YAAAtE,QAAA,eACd7E,OAAA,CAACzC,GAAG;cAAC8L,IAAI,EAAE,EAAG;cAAAxE,QAAA,eACZ7E,OAAA,CAACI,KAAK;gBAACoJ,KAAK,EAAE,CAAE;gBAAA3E,QAAA,EACb,EAACY,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE7B,KAAK,IACT3B,UAAU,CAACwD,IAAI,CAACe,GAAG,CAAC,GACpB7G,aAAa,CACXsC,UAAU,CAACwD,IAAI,CAACe,GAAG,CAAC,EACpB/D,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,MAAM,EACvBpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,QACnB;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAtBFgB,IAAI,CAACa,KAAK;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNzE,OAAA,CAACrB,WAAW;MAACsD,UAAU,EAAEA,UAAW;MAACwH,UAAU,EAAC;IAAgB;MAAAnF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnEzE,OAAA,CAAC1C,IAAI;MAAAuH,QAAA,gBACH7E,OAAA,CAACvC,KAAK;QAAC2L,SAAS,EAAC,6CAA6C;QAAAvE,QAAA,gBAC5D7E,OAAA,CAACtC,UAAU,CAACyC,IAAI;UAACuJ,MAAM;UAACF,KAAK,EAAE,CAAE;UAAA3E,QAAA,EAC9BnF,CAAC,CAAC,UAAU;QAAC;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAClBzE,OAAA,CAACvC,KAAK;UAAC2L,SAAS,EAAC,gCAAgC;UAAAvE,QAAA,gBAC/C7E,OAAA,CAAC5B,WAAW;YACVuL,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAQ,CAAE;YAC7BC,YAAY,EAAGC,CAAC,IAAKtH,SAAS,CAACsH,CAAC;UAAE;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFzE,OAAA,CAACF,cAAc;YACbiK,YAAY,EAAEhD,UAAW;YACzBiD,WAAW,EAAEtK,CAAC,CAAC,aAAa,CAAE;YAC9BuK,QAAQ,EAAG1D,KAAK,IAAKpF,SAAS,CAACoF,KAAK,CAACA,KAAK,CAAE;YAC5C2D,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAAC;UAAE;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACFzE,OAAA,CAACnC,MAAM;YACL8I,IAAI,EACFT,OAAO,CAAC9D,eAAe,CAAC+H,MAAM,CAAC,IAC/B,CAAC,CAACrJ,WAAW,IACb,CAAC,CAACG,UAAU,IACZ,CAAC,CAACC,MAAM,GACJ,SAAS,GACT,SACL;YACDkJ,MAAM,EACJlE,OAAO,CAAC9D,eAAe,CAAC+H,MAAM,CAAC,IAC/B,CAAC,CAACrJ,WAAW,IACb,CAAC,CAACG,UAAU,IACZ,CAAC,CAACC,MACH;YACDqI,OAAO,EAAEP,KAAM;YAAAnE,QAAA,EAEdnF,CAAC,CAAC,OAAO;UAAC;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACTzE,OAAA,CAACnC,MAAM;YACLwM,IAAI,eAAErK,OAAA,CAAC3B,qBAAqB;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC1C,OAAO,EAAEO,WAAY;YACrBiH,OAAO,EAAEtB,WAAY;YAAApD,QAAA,EAEpBnF,CAAC,CAAC,UAAU;UAAC;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACTzE,OAAA,CAAClB,aAAa;YAAC+D,OAAO,EAAEA,OAAQ;YAAC6B,UAAU,EAAEA;UAAW;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAERzE,OAAA,CAACrC,KAAK;QACJ2M,UAAU,EAAE;UACV3H,iBAAiB;UACjB4H,sBAAsB,EAAE,CAAC,GAAG;QAC9B,CAAE;QACFnC,YAAY,EAAEA,YAAa;QAC3BvF,OAAO,EAAEA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2H,MAAM,CAAE/E,IAAI,IAAKA,IAAI,CAACX,OAAO,CAAE;QACjDb,UAAU,GAAAzD,iBAAA,GAAE0B,WAAW,CAACgB,IAAI,cAAA1C,iBAAA,uBAAhBA,iBAAA,CAAkB0C,IAAK;QACnCuH,MAAM,EAAG7H,GAAG,IAAKA,GAAG,CAACgC,EAAG;QACxB7C,OAAO,EAAEA,OAAQ;QACjBoC,UAAU,EAAE;UACV4D,QAAQ,EAAE3G,QAAQ;UAClBuG,IAAI,EAAE,CAAAzF,WAAW,aAAXA,WAAW,wBAAAzB,kBAAA,GAAXyB,WAAW,CAAEgB,IAAI,cAAAzC,kBAAA,uBAAjBA,kBAAA,CAAmBiK,IAAI,CAAC/C,IAAI,KAAI,CAAC;UACvCgD,KAAK,EAAEzI,WAAW,aAAXA,WAAW,wBAAAxB,kBAAA,GAAXwB,WAAW,CAAEgB,IAAI,cAAAxC,kBAAA,uBAAjBA,kBAAA,CAAmBgK,IAAI,CAACC,KAAK;UACpCC,cAAc,EAAE;QAClB,CAAE;QACF/B,QAAQ,EAAEf,kBAAmB;QAC7B/D,MAAM,EAAE;UACNC,CAAC,EAAE;QACL;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAAClE,EAAA,CAnaID,cAAc;EAAA,QACD7B,WAAW,EACXc,WAAW,EACXC,WAAW,EAQLd,WAAW,EAM9BA,WAAW,EAKaA,WAAW,EAmNvCW,YAAY,EAIZA,YAAY;AAAA;AAAAwL,EAAA,GA7ORvK,cAAc;AAqapB,eAAeA,cAAc;AAAC,IAAAuK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}