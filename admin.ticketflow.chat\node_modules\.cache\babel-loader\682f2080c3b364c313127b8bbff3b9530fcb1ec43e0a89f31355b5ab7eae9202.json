{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\ckeEditor.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Form } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { IMG_URL } from '../configs/app-global';\nimport galleryService from '../services/gallery';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CkeEditor({\n  form,\n  lang,\n  languages\n}) {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  function uploadAdapter(loader) {\n    return {\n      upload: () => {\n        return new Promise((resolve, reject) => {\n          const formData = new FormData();\n          loader.file.then(file => {\n            const isItAtLeast2MB = file.size / 1024 / 1024 < 2;\n            if (!isItAtLeast2MB) {\n              toast.error(t('min.2.mb'));\n              reject();\n            }\n            formData.append('image', file);\n            formData.append('type', 'blogs');\n            galleryService.upload(formData).then(({\n              data\n            }) => {\n              resolve({\n                default: `${IMG_URL + data.title}`\n              });\n            }).catch(err => {\n              reject(err);\n            });\n          });\n        });\n      }\n    };\n  }\n  function uploadPlugin(editor) {\n    editor.plugins.get('FileRepository').createUploadAdapter = loader => {\n      return uploadAdapter(loader);\n    };\n  }\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: languages.map(item => /*#__PURE__*/_jsxDEV(Form.Item, {\n      label: t('description'),\n      name: `description[${item.locale}]`,\n      valuePropName: \"data\",\n      getValueFromEvent: (event, editor) => {\n        const data = editor.getData();\n        return data;\n      },\n      rules: [{\n        required: item.locale === lang,\n        message: t('required')\n      }],\n      hidden: item.locale !== lang,\n      children: /*#__PURE__*/_jsxDEV(CKEditor, {\n        editor: ClassicEditor,\n        data: form.getFieldValue(`description[${item.locale}]`) || '',\n        config: {\n          extraPlugins: [uploadPlugin]\n        },\n        onReady: editor => {\n          // Editor is ready to use\n        },\n        onError: (error, {\n          willEditorRestart\n        }) => {\n          if (willEditorRestart) {\n            editor.ui.view.toolbar.element.style.display = 'none';\n          }\n        },\n        onBlur: (event, editor) => {\n          const data = editor.getData();\n          form.setFieldsValue({\n            [`description[${item.value}]`]: data\n          });\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this)\n    }, 'desc' + item.locale, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n}\n_s(CkeEditor, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function () {\n  return [useTranslation];\n});\n_c = CkeEditor;\nvar _c;\n$RefreshReg$(_c, \"CkeEditor\");", "map": {"version": 3, "names": ["React", "Form", "useTranslation", "CKEditor", "ClassicEditor", "IMG_URL", "galleryService", "toast", "jsxDEV", "_jsxDEV", "CkeEditor", "form", "lang", "languages", "_s", "t", "uploadAdapter", "loader", "upload", "Promise", "resolve", "reject", "formData", "FormData", "file", "then", "isItAtLeast2MB", "size", "error", "append", "data", "default", "title", "catch", "err", "uploadPlugin", "editor", "plugins", "get", "createUploadAdapter", "Fragment", "children", "map", "item", "<PERSON><PERSON>", "label", "name", "locale", "valuePropName", "getValueFromEvent", "event", "getData", "rules", "required", "message", "hidden", "getFieldValue", "config", "extraPlugins", "onReady", "onError", "willEditorRestart", "ui", "view", "toolbar", "element", "style", "display", "onBlur", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/ckeEditor.js"], "sourcesContent": ["import React from 'react';\nimport { Form } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { IMG_URL } from '../configs/app-global';\nimport galleryService from '../services/gallery';\nimport { toast } from 'react-toastify';\n\nexport default function CkeEditor({ form, lang, languages }) {\n  const { t } = useTranslation();\n\n  function uploadAdapter(loader) {\n    return {\n      upload: () => {\n        return new Promise((resolve, reject) => {\n          const formData = new FormData();\n          loader.file.then((file) => {\n            const isItAtLeast2MB = file.size / 1024 / 1024 < 2;\n            if (!isItAtLeast2MB) {\n              toast.error(t('min.2.mb'));\n              reject();\n            }\n            formData.append('image', file);\n            formData.append('type', 'blogs');\n            galleryService\n              .upload(formData)\n              .then(({ data }) => {\n                resolve({\n                  default: `${IMG_URL + data.title}`,\n                });\n              })\n              .catch((err) => {\n                reject(err);\n              });\n          });\n        });\n      },\n    };\n  }\n\n  function uploadPlugin(editor) {\n    editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {\n      return uploadAdapter(loader);\n    };\n  }\n\n  return (\n    <React.Fragment>\n      {languages.map((item) => (\n        <Form.Item\n          key={'desc' + item.locale}\n          label={t('description')}\n          name={`description[${item.locale}]`}\n          valuePropName='data'\n          getValueFromEvent={(event, editor) => {\n            const data = editor.getData();\n            return data;\n          }}\n          rules={[\n            {\n              required: item.locale === lang,\n              message: t('required'),\n            },\n          ]}\n          hidden={item.locale !== lang}\n        >\n          <CKEditor\n            editor={ClassicEditor}\n            data={form.getFieldValue(`description[${item.locale}]`) || ''}\n            config={{\n              extraPlugins: [uploadPlugin],\n            }}\n            onReady={(editor) => {\n              // Editor is ready to use\n            }}\n            onError={(error, { willEditorRestart }) => {\n              if (willEditorRestart) {\n                editor.ui.view.toolbar.element.style.display = 'none';\n              }\n            }}\n            onBlur={(event, editor) => {\n              const data = editor.getData();\n              form.setFieldsValue({\n                [`description[${item.value}]`]: data,\n              });\n            }}\n          />\n        </Form.Item>\n      ))}\n    </React.Fragment>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,OAAOC,cAAc,MAAM,qBAAqB;AAChD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,eAAe,SAASC,SAASA,CAAC;EAAEC,IAAI;EAAEC,IAAI;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAC3D,MAAM;IAAEC;EAAE,CAAC,GAAGb,cAAc,CAAC,CAAC;EAE9B,SAASc,aAAaA,CAACC,MAAM,EAAE;IAC7B,OAAO;MACLC,MAAM,EAAEA,CAAA,KAAM;QACZ,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACtC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAC/BN,MAAM,CAACO,IAAI,CAACC,IAAI,CAAED,IAAI,IAAK;YACzB,MAAME,cAAc,GAAGF,IAAI,CAACG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;YAClD,IAAI,CAACD,cAAc,EAAE;cACnBnB,KAAK,CAACqB,KAAK,CAACb,CAAC,CAAC,UAAU,CAAC,CAAC;cAC1BM,MAAM,CAAC,CAAC;YACV;YACAC,QAAQ,CAACO,MAAM,CAAC,OAAO,EAAEL,IAAI,CAAC;YAC9BF,QAAQ,CAACO,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;YAChCvB,cAAc,CACXY,MAAM,CAACI,QAAQ,CAAC,CAChBG,IAAI,CAAC,CAAC;cAAEK;YAAK,CAAC,KAAK;cAClBV,OAAO,CAAC;gBACNW,OAAO,EAAG,GAAE1B,OAAO,GAAGyB,IAAI,CAACE,KAAM;cACnC,CAAC,CAAC;YACJ,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,IAAK;cACdb,MAAM,CAACa,GAAG,CAAC;YACb,CAAC,CAAC;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;EACH;EAEA,SAASC,YAAYA,CAACC,MAAM,EAAE;IAC5BA,MAAM,CAACC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAACC,mBAAmB,GAAItB,MAAM,IAAK;MACrE,OAAOD,aAAa,CAACC,MAAM,CAAC;IAC9B,CAAC;EACH;EAEA,oBACER,OAAA,CAACT,KAAK,CAACwC,QAAQ;IAAAC,QAAA,EACZ5B,SAAS,CAAC6B,GAAG,CAAEC,IAAI,iBAClBlC,OAAA,CAACR,IAAI,CAAC2C,IAAI;MAERC,KAAK,EAAE9B,CAAC,CAAC,aAAa,CAAE;MACxB+B,IAAI,EAAG,eAAcH,IAAI,CAACI,MAAO,GAAG;MACpCC,aAAa,EAAC,MAAM;MACpBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEd,MAAM,KAAK;QACpC,MAAMN,IAAI,GAAGM,MAAM,CAACe,OAAO,CAAC,CAAC;QAC7B,OAAOrB,IAAI;MACb,CAAE;MACFsB,KAAK,EAAE,CACL;QACEC,QAAQ,EAAEV,IAAI,CAACI,MAAM,KAAKnC,IAAI;QAC9B0C,OAAO,EAAEvC,CAAC,CAAC,UAAU;MACvB,CAAC,CACD;MACFwC,MAAM,EAAEZ,IAAI,CAACI,MAAM,KAAKnC,IAAK;MAAA6B,QAAA,eAE7BhC,OAAA,CAACN,QAAQ;QACPiC,MAAM,EAAEhC,aAAc;QACtB0B,IAAI,EAAEnB,IAAI,CAAC6C,aAAa,CAAE,eAAcb,IAAI,CAACI,MAAO,GAAE,CAAC,IAAI,EAAG;QAC9DU,MAAM,EAAE;UACNC,YAAY,EAAE,CAACvB,YAAY;QAC7B,CAAE;QACFwB,OAAO,EAAGvB,MAAM,IAAK;UACnB;QAAA,CACA;QACFwB,OAAO,EAAEA,CAAChC,KAAK,EAAE;UAAEiC;QAAkB,CAAC,KAAK;UACzC,IAAIA,iBAAiB,EAAE;YACrBzB,MAAM,CAAC0B,EAAE,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UACvD;QACF,CAAE;QACFC,MAAM,EAAEA,CAAClB,KAAK,EAAEd,MAAM,KAAK;UACzB,MAAMN,IAAI,GAAGM,MAAM,CAACe,OAAO,CAAC,CAAC;UAC7BxC,IAAI,CAAC0D,cAAc,CAAC;YAClB,CAAE,eAAc1B,IAAI,CAAC2B,KAAM,GAAE,GAAGxC;UAClC,CAAC,CAAC;QACJ;MAAE;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GApCG,MAAM,GAAG/B,IAAI,CAACI,MAAM;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAqChB,CACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAErB;AAAC5D,EAAA,CAnFuBJ,SAAS;EAAA,QACjBR,cAAc;AAAA;AAAAyE,EAAA,GADNjE,SAAS;AAAA,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}