{"__meta": {"id": "Xd5952502d9da9801c33a74eddad0fcdc", "datetime": "2025-07-21 19:24:38", "utime": 1753136678.683634, "method": "GET", "uri": "/api/v1/dashboard/admin/receipts?lang=pt-BR", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[19:24:38] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753136678.559886, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753136678.314996, "end": 1753136678.683652, "duration": 0.3686559200286865, "duration_str": "369ms", "measures": [{"label": "Booting", "start": 1753136678.314996, "relative_start": 0, "end": 1753136678.543631, "relative_end": 1753136678.543631, "duration": 0.22863507270812988, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753136678.543643, "relative_start": 0.22864699363708496, "end": 1753136678.683655, "relative_end": 3.0994415283203125e-06, "duration": 0.14001202583312988, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 42853280, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/dashboard/admin/receipts", "middleware": "api, block.ip, sanctum.check, role:admin|manager", "as": "admin.receipts.index", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController@index", "namespace": null, "prefix": "api/v1/dashboard/admin", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php&line=32\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php:32-37</a>"}, "queries": {"nb_statements": 23, "nb_failed_statements": 0, "accumulated_duration": 0.024589999999999997, "accumulated_duration_str": "24.59ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.01569, "duration_str": "15.69ms", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 0, "width_percent": 63.806}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 63.806, "width_percent": 1.22}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 65.026, "width_percent": 1.342}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 66.368, "width_percent": 1.22}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '55' limit 1", "type": "query", "params": [], "bindings": ["55"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 67.588, "width_percent": 1.993}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 69.581, "width_percent": 1.749}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-21 19:24:38', `personal_access_tokens`.`updated_at` = '2025-07-21 19:24:38' where `id` = 55", "type": "query", "params": [], "bindings": ["2025-07-21 19:24:38", "2025-07-21 19:24:38", "55"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 71.33, "width_percent": 5.653}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (101) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 23, "namespace": "middleware", "name": "role", "line": 29}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 18}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 76.983, "width_percent": 2.033}, {"sql": "select count(*) as aggregate from `languages` where `locale` = 'pt-BR'", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 79.016, "width_percent": 1.871}, {"sql": "select count(*) as aggregate from `receipts` where `receipts`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 80.887, "width_percent": 1.871}, {"sql": "select * from `receipts` where `receipts`.`deleted_at` is null order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 82.757, "width_percent": 1.464}, {"sql": "select `locale`, `title`, `receipt_id` from `receipt_translations` where (`receipt_translations`.`receipt_id` in (1) and `locale` = 'pt-BR' or `locale` = 'pt-BR') and `receipt_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 84.221, "width_percent": 1.871}, {"sql": "select `locale`, `title`, `receipt_id` from `receipt_ingredients` where (`receipt_ingredients`.`receipt_id` in (1) and `locale` = 'pt-BR' or `locale` = 'pt-BR') and `receipt_ingredients`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 86.092, "width_percent": 1.627}, {"sql": "select `locale`, `title`, `receipt_id` from `receipt_instructions` where (`receipt_instructions`.`receipt_id` in (1) and `locale` = 'pt-BR' or `locale` = 'pt-BR') and `receipt_instructions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 87.719, "width_percent": 1.423}, {"sql": "select * from `receipt_nutritions` where `receipt_nutritions`.`receipt_id` in (1) and `receipt_nutritions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 89.142, "width_percent": 1.301}, {"sql": "select `locale`, `title`, `nutrition_id` from `receipt_nutrition_translations` where `receipt_nutrition_translations`.`nutrition_id` in (1) and `locale` = 'pt-BR' and `receipt_nutrition_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 90.443, "width_percent": 1.423}, {"sql": "select * from `receipt_nutrition_translations` where `receipt_nutrition_translations`.`nutrition_id` in (1) and `receipt_nutrition_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 91.867, "width_percent": 1.098}, {"sql": "select `id`, `logo_img`, `uuid` from `shops` where `shops`.`id` in (502) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 92.965, "width_percent": 1.098}, {"sql": "select * from `categories` where `categories`.`id` in (14) and `categories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 94.063, "width_percent": 1.179}, {"sql": "select `locale`, `title`, `category_id` from `category_translations` where (`category_translations`.`category_id` in (14) and `locale` = 'pt-BR' or `locale` = 'pt-BR') and `category_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 95.242, "width_percent": 1.383}, {"sql": "select * from `receipt_translations` where `receipt_translations`.`receipt_id` in (1) and `receipt_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 96.625, "width_percent": 1.301}, {"sql": "select * from `receipt_ingredients` where `receipt_ingredients`.`receipt_id` in (1) and `receipt_ingredients`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 97.926, "width_percent": 1.098}, {"sql": "select * from `receipt_instructions` where `receipt_instructions`.`receipt_id` in (1) and `receipt_instructions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ReceiptController.php", "line": 34}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00023999999999999998, "duration_str": "240μs", "stmt_id": "\\app\\Repositories\\ReceiptRepository\\ReceiptRepository.php:50", "connection": "foodyman", "start_percent": 99.024, "width_percent": 0.976}]}, "models": {"data": {"App\\Models\\ReceiptInstruction": 1, "App\\Models\\ReceiptIngredient": 1, "App\\Models\\ReceiptTranslation": 1, "App\\Models\\CategoryTranslation": 4, "App\\Models\\Category": 1, "App\\Models\\ReceiptNutritionTranslation": 1, "App\\Models\\ReceiptNutrition": 1, "App\\Models\\Receipt": 1, "Spatie\\Permission\\Models\\Role": 1, "App\\Models\\User": 1, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 2, "App\\Models\\Language": 2}, "count": 18}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7264e5-cae6-4f2c-a161-6be318e4705b\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/admin/receipts", "status_code": "<pre class=sf-dump id=sf-dump-1043155223 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1043155223\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-736376294 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736376294\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-834835052 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834835052\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1835926170 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835926170\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2028889588 data-indent-pad=\"  \"><span class=sf-dump-note>array:33</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56983</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/api/v1/dashboard/admin/receipts?lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/api/v1/dashboard/admin/receipts</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"42 characters\">/index.php/api/v1/dashboard/admin/receipts</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753136678.315</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753136678</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028889588\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1994017104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1994017104\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-38083129 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 22:24:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4995</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38083129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1682597542 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1682597542\", {\"maxDepth\":0})</script>\n"}}