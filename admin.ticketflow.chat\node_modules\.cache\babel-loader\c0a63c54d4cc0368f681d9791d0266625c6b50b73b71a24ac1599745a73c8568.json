{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\addons\\\\productStatusModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Button, Col, Form, Modal, Row, Select } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport productService from '../../services/product';\nimport { setRefetch } from '../../redux/slices/menu';\nimport { fetchAddons } from '../../redux/slices/addons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst allStatuses = ['published', 'pending', 'unpublished'];\nexport default function ProductStatusModal({\n  orderDetails: data,\n  handleCancel\n}) {\n  _s();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const {\n    t\n  } = useTranslation();\n  const [loading, setLoading] = useState(false);\n  const onFinish = values => {\n    setLoading(true);\n    const params = {\n      ...values\n    };\n    productService.updateStatus(data.uuid, params).then(() => {\n      handleCancel();\n      const data = activeMenu.data;\n      const paramsData = {\n        status: (data === null || data === void 0 ? void 0 : data.role) === 'deleted_at' ? null : (data === null || data === void 0 ? void 0 : data.role) || 'published',\n        deleted_at: (data === null || data === void 0 ? void 0 : data.role) === 'deleted_at' ? data.role : null,\n        perPage: data === null || data === void 0 ? void 0 : data.perPage,\n        page: data === null || data === void 0 ? void 0 : data.page\n      };\n      dispatch(fetchAddons(paramsData));\n      dispatch(setRefetch(activeMenu));\n    }).finally(() => setLoading(false));\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    visible: !!data,\n    title: data.title,\n    onCancel: handleCancel,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => form.submit(),\n      loading: loading,\n      children: t('save')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"default\",\n      onClick: handleCancel,\n      children: t('cancel')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      layout: \"vertical\",\n      onFinish: onFinish,\n      initialValues: {\n        status: data.status\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 12,\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: t('status'),\n            name: \"status\",\n            rules: [{\n              required: true,\n              message: t('required')\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              children: allStatuses.map((item, idx) => /*#__PURE__*/_jsxDEV(Select.Option, {\n                value: item,\n                children: t(item)\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductStatusModal, \"lZUNXHZMuGnz6PrIpiE+t/TZbNo=\", false, function () {\n  return [useSelector, Form.useForm, useDispatch, useTranslation];\n});\n_c = ProductStatusModal;\nvar _c;\n$RefreshReg$(_c, \"ProductStatusModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "<PERSON><PERSON>", "Col", "Form", "Modal", "Row", "Select", "shallowEqual", "useDispatch", "useSelector", "useTranslation", "productService", "setRefetch", "fetchAddons", "jsxDEV", "_jsxDEV", "allStatuses", "ProductStatusModal", "orderDetails", "data", "handleCancel", "_s", "activeMenu", "state", "menu", "form", "useForm", "dispatch", "t", "loading", "setLoading", "onFinish", "values", "params", "updateStatus", "uuid", "then", "paramsData", "status", "role", "deleted_at", "perPage", "page", "finally", "visible", "title", "onCancel", "footer", "type", "onClick", "submit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "initialValues", "gutter", "span", "<PERSON><PERSON>", "label", "name", "rules", "required", "message", "map", "item", "idx", "Option", "value", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/addons/productStatusModal.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Button, Col, Form, Modal, Row, Select } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport productService from '../../services/product';\nimport { setRefetch } from '../../redux/slices/menu';\nimport { fetchAddons } from '../../redux/slices/addons';\n\nconst allStatuses = ['published', 'pending', 'unpublished'];\n\nexport default function ProductStatusModal({\n  orderDetails: data,\n  handleCancel,\n}) {\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(false);\n\n  const onFinish = (values) => {\n    setLoading(true);\n    const params = { ...values };\n    productService\n      .updateStatus(data.uuid, params)\n      .then(() => {\n        handleCancel();\n        const data = activeMenu.data;\n        const paramsData = {\n          status:\n            data?.role === 'deleted_at' ? null : data?.role || 'published',\n          deleted_at: data?.role === 'deleted_at' ? data.role : null,\n          perPage: data?.perPage,\n          page: data?.page,\n        };\n        dispatch(fetchAddons(paramsData));\n        dispatch(setRefetch(activeMenu));\n      })\n      .finally(() => setLoading(false));\n  };\n\n  return (\n    <Modal\n      visible={!!data}\n      title={data.title}\n      onCancel={handleCancel}\n      footer={[\n        <Button type='primary' onClick={() => form.submit()} loading={loading}>\n          {t('save')}\n        </Button>,\n        <Button type='default' onClick={handleCancel}>\n          {t('cancel')}\n        </Button>,\n      ]}\n    >\n      <Form\n        form={form}\n        layout='vertical'\n        onFinish={onFinish}\n        initialValues={{ status: data.status }}\n      >\n        <Row gutter={12}>\n          <Col span={24}>\n            <Form.Item\n              label={t('status')}\n              name='status'\n              rules={[\n                {\n                  required: true,\n                  message: t('required'),\n                },\n              ]}\n            >\n              <Select>\n                {allStatuses.map((item, idx) => (\n                  <Select.Option key={idx} value={item}>\n                    {t(item)}\n                  </Select.Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n      </Form>\n    </Modal>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,QAAQ,MAAM;AAC5D,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,WAAW,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC;AAE3D,eAAe,SAASC,kBAAkBA,CAAC;EACzCC,YAAY,EAAEC,IAAI;EAClBC;AACF,CAAC,EAAE;EAAAC,EAAA;EACD,MAAM;IAAEC;EAAW,CAAC,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEjB,YAAY,CAAC;EACvE,MAAM,CAACkB,IAAI,CAAC,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoB;EAAE,CAAC,GAAGlB,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMiC,QAAQ,GAAIC,MAAM,IAAK;IAC3BF,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMG,MAAM,GAAG;MAAE,GAAGD;IAAO,CAAC;IAC5BrB,cAAc,CACXuB,YAAY,CAACf,IAAI,CAACgB,IAAI,EAAEF,MAAM,CAAC,CAC/BG,IAAI,CAAC,MAAM;MACVhB,YAAY,CAAC,CAAC;MACd,MAAMD,IAAI,GAAGG,UAAU,CAACH,IAAI;MAC5B,MAAMkB,UAAU,GAAG;QACjBC,MAAM,EACJ,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,MAAK,YAAY,GAAG,IAAI,GAAG,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,KAAI,WAAW;QAChEC,UAAU,EAAE,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,MAAK,YAAY,GAAGpB,IAAI,CAACoB,IAAI,GAAG,IAAI;QAC1DE,OAAO,EAAEtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,OAAO;QACtBC,IAAI,EAAEvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB;MACd,CAAC;MACDf,QAAQ,CAACd,WAAW,CAACwB,UAAU,CAAC,CAAC;MACjCV,QAAQ,CAACf,UAAU,CAACU,UAAU,CAAC,CAAC;IAClC,CAAC,CAAC,CACDqB,OAAO,CAAC,MAAMb,UAAU,CAAC,KAAK,CAAC,CAAC;EACrC,CAAC;EAED,oBACEf,OAAA,CAACX,KAAK;IACJwC,OAAO,EAAE,CAAC,CAACzB,IAAK;IAChB0B,KAAK,EAAE1B,IAAI,CAAC0B,KAAM;IAClBC,QAAQ,EAAE1B,YAAa;IACvB2B,MAAM,EAAE,cACNhC,OAAA,CAACd,MAAM;MAAC+C,IAAI,EAAC,SAAS;MAACC,OAAO,EAAEA,CAAA,KAAMxB,IAAI,CAACyB,MAAM,CAAC,CAAE;MAACrB,OAAO,EAAEA,OAAQ;MAAAsB,QAAA,EACnEvB,CAAC,CAAC,MAAM;IAAC;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACTxC,OAAA,CAACd,MAAM;MAAC+C,IAAI,EAAC,SAAS;MAACC,OAAO,EAAE7B,YAAa;MAAA+B,QAAA,EAC1CvB,CAAC,CAAC,QAAQ;IAAC;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,CACT;IAAAJ,QAAA,eAEFpC,OAAA,CAACZ,IAAI;MACHsB,IAAI,EAAEA,IAAK;MACX+B,MAAM,EAAC,UAAU;MACjBzB,QAAQ,EAAEA,QAAS;MACnB0B,aAAa,EAAE;QAAEnB,MAAM,EAAEnB,IAAI,CAACmB;MAAO,CAAE;MAAAa,QAAA,eAEvCpC,OAAA,CAACV,GAAG;QAACqD,MAAM,EAAE,EAAG;QAAAP,QAAA,eACdpC,OAAA,CAACb,GAAG;UAACyD,IAAI,EAAE,EAAG;UAAAR,QAAA,eACZpC,OAAA,CAACZ,IAAI,CAACyD,IAAI;YACRC,KAAK,EAAEjC,CAAC,CAAC,QAAQ,CAAE;YACnBkC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAE,CACL;cACEC,QAAQ,EAAE,IAAI;cACdC,OAAO,EAAErC,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YAAAuB,QAAA,eAEFpC,OAAA,CAACT,MAAM;cAAA6C,QAAA,EACJnC,WAAW,CAACkD,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACzBrD,OAAA,CAACT,MAAM,CAAC+D,MAAM;gBAAWC,KAAK,EAAEH,IAAK;gBAAAhB,QAAA,EAClCvB,CAAC,CAACuC,IAAI;cAAC,GADUC,GAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAAClC,EAAA,CA5EuBJ,kBAAkB;EAAA,QAIjBR,WAAW,EACnBN,IAAI,CAACuB,OAAO,EACVlB,WAAW,EACdE,cAAc;AAAA;AAAA6D,EAAA,GAPNtD,kBAAkB;AAAA,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}