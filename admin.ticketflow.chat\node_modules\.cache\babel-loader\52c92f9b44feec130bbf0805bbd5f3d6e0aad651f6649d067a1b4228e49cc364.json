{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\infinite-select.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useEffect, useRef } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InfiniteSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  hasMore,\n  refetchOnFocus = false,\n  ...props\n}) => {\n  _s();\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [search, setSearch] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const isMountedRef = useRef(true);\n  const debounceFetcher = useMemo(() => {\n    const loadOptions = value => {\n      if (!isMountedRef.current) return;\n      setOptions([]);\n      setSearch(value);\n      setFetching(true);\n      fetchOptions({\n        search: value\n      }).then(newOptions => {\n        if (isMountedRef.current) {\n          setOptions(newOptions);\n          setCurrentPage(2);\n          setFetching(false);\n        }\n      }).finally(() => {\n        if (isMountedRef.current) {\n          setLoading(false);\n        }\n      });\n    };\n    return debounce(loadOptions, debounceTimeout);\n  }, [fetchOptions, debounceTimeout, currentPage]);\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n      debounceFetcher.cancel();\n    };\n  }, [debounceFetcher]);\n  const fetchOnFocus = () => {\n    if (refetchOnFocus) {\n      debounceFetcher('');\n    } else {\n      if (!(options !== null && options !== void 0 && options.length)) {\n        debounceFetcher('');\n      }\n    }\n  };\n  const onScroll = async event => {\n    const target = event.target;\n    if (!loading && target.scrollTop + target.offsetHeight === target.scrollHeight) {\n      if (hasMore && isMountedRef.current) {\n        setLoading(true);\n        // target.scrollTo(0, target.scrollHeight);\n        fetchOptions({\n          search: search,\n          page: currentPage\n        }).then(item => {\n          if (isMountedRef.current) {\n            setCurrentPage(i => i + 1);\n            setOptions([...options, ...item]);\n          }\n        }).finally(() => {\n          if (isMountedRef.current) {\n            setLoading(false);\n          }\n        });\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Select, {\n    showSearch: true,\n    allowClear: true,\n    onPopupScroll: onScroll,\n    labelInValue: true,\n    filterOption: false,\n    onSearch: debounceFetcher,\n    notFoundContent: fetching ? /*#__PURE__*/_jsxDEV(Spin, {\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 35\n    }, this) : 'no results',\n    onFocus: fetchOnFocus,\n    ...props,\n    children: [options.map((item, index) => /*#__PURE__*/_jsxDEV(Select.Option, {\n      value: item.value,\n      children: item.label\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 9\n    }, this)), loading && /*#__PURE__*/_jsxDEV(Select.Option, {\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(InfiniteSelect, \"uUnEPZdPrI/fXrqH5a5mtQAIoTw=\");\n_c = InfiniteSelect;\nvar _c;\n$RefreshReg$(_c, \"InfiniteSelect\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useEffect", "useRef", "debounce", "Select", "Spin", "jsxDEV", "_jsxDEV", "InfiniteSelect", "fetchOptions", "debounceTimeout", "hasMore", "refetchOnFocus", "props", "_s", "fetching", "setFetching", "options", "setOptions", "loading", "setLoading", "search", "setSearch", "currentPage", "setCurrentPage", "isMountedRef", "deboun<PERSON><PERSON><PERSON><PERSON>", "loadOptions", "value", "current", "then", "newOptions", "finally", "cancel", "fetchOnFocus", "length", "onScroll", "event", "target", "scrollTop", "offsetHeight", "scrollHeight", "page", "item", "i", "showSearch", "allowClear", "onPopupScroll", "labelInValue", "filterOption", "onSearch", "notFoundContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onFocus", "children", "map", "index", "Option", "label", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/infinite-select.js"], "sourcesContent": ["import React, { useState, useMemo, useEffect, useRef } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\n\nexport const InfiniteSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  hasMore,\n  refetchOnFocus = false,\n  ...props\n}) => {\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [search, setSearch] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const isMountedRef = useRef(true);\n\n  const debounceFetcher = useMemo(() => {\n    const loadOptions = (value) => {\n      if (!isMountedRef.current) return;\n\n      setOptions([]);\n      setSearch(value);\n      setFetching(true);\n      fetchOptions({ search: value })\n        .then((newOptions) => {\n          if (isMountedRef.current) {\n            setOptions(newOptions);\n            setCurrentPage(2);\n            setFetching(false);\n          }\n        })\n        .finally(() => {\n          if (isMountedRef.current) {\n            setLoading(false);\n          }\n        });\n    };\n    return debounce(loadOptions, debounceTimeout);\n  }, [fetchOptions, debounceTimeout, currentPage]);\n\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n      debounceFetcher.cancel();\n    };\n  }, [debounceFetcher]);\n\n  const fetchOnFocus = () => {\n    if (refetchOnFocus) {\n      debounceFetcher('');\n    } else {\n      if (!options?.length) {\n        debounceFetcher('');\n      }\n    }\n  };\n\n  const onScroll = async (event) => {\n    const target = event.target;\n    if (\n      !loading &&\n      target.scrollTop + target.offsetHeight === target.scrollHeight\n    ) {\n      if (hasMore && isMountedRef.current) {\n        setLoading(true);\n        // target.scrollTo(0, target.scrollHeight);\n        fetchOptions({ search: search, page: currentPage })\n          .then((item) => {\n            if (isMountedRef.current) {\n              setCurrentPage((i) => i + 1);\n              setOptions([...options, ...item]);\n            }\n          })\n          .finally(() => {\n            if (isMountedRef.current) {\n              setLoading(false);\n            }\n          });\n      }\n    }\n  };\n\n  return (\n    <Select\n      showSearch\n      allowClear\n      onPopupScroll={onScroll}\n      labelInValue={true}\n      filterOption={false}\n      onSearch={debounceFetcher}\n      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}\n      onFocus={fetchOnFocus}\n      {...props}\n    >\n      {options.map((item, index) => (\n        <Select.Option key={index} value={item.value}>\n          {item.label}\n        </Select.Option>\n      ))}\n      {loading && (\n        <Select.Option>\n          <Spin size='small' />\n        </Select.Option>\n      )}\n    </Select>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BC,YAAY;EACZC,eAAe,GAAG,GAAG;EACrBC,OAAO;EACPC,cAAc,GAAG,KAAK;EACtB,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM0B,YAAY,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMwB,eAAe,GAAG1B,OAAO,CAAC,MAAM;IACpC,MAAM2B,WAAW,GAAIC,KAAK,IAAK;MAC7B,IAAI,CAACH,YAAY,CAACI,OAAO,EAAE;MAE3BX,UAAU,CAAC,EAAE,CAAC;MACdI,SAAS,CAACM,KAAK,CAAC;MAChBZ,WAAW,CAAC,IAAI,CAAC;MACjBP,YAAY,CAAC;QAAEY,MAAM,EAAEO;MAAM,CAAC,CAAC,CAC5BE,IAAI,CAAEC,UAAU,IAAK;QACpB,IAAIN,YAAY,CAACI,OAAO,EAAE;UACxBX,UAAU,CAACa,UAAU,CAAC;UACtBP,cAAc,CAAC,CAAC,CAAC;UACjBR,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC,CACDgB,OAAO,CAAC,MAAM;QACb,IAAIP,YAAY,CAACI,OAAO,EAAE;UACxBT,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,CAAC;IACN,CAAC;IACD,OAAOjB,QAAQ,CAACwB,WAAW,EAAEjB,eAAe,CAAC;EAC/C,CAAC,EAAE,CAACD,YAAY,EAAEC,eAAe,EAAEa,WAAW,CAAC,CAAC;EAEhDtB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwB,YAAY,CAACI,OAAO,GAAG,KAAK;MAC5BH,eAAe,CAACO,MAAM,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACP,eAAe,CAAC,CAAC;EAErB,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAItB,cAAc,EAAE;MAClBc,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,MAAM;MACL,IAAI,EAACT,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEkB,MAAM,GAAE;QACpBT,eAAe,CAAC,EAAE,CAAC;MACrB;IACF;EACF,CAAC;EAED,MAAMU,QAAQ,GAAG,MAAOC,KAAK,IAAK;IAChC,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IACE,CAACnB,OAAO,IACRmB,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACE,YAAY,KAAKF,MAAM,CAACG,YAAY,EAC9D;MACA,IAAI9B,OAAO,IAAIc,YAAY,CAACI,OAAO,EAAE;QACnCT,UAAU,CAAC,IAAI,CAAC;QAChB;QACAX,YAAY,CAAC;UAAEY,MAAM,EAAEA,MAAM;UAAEqB,IAAI,EAAEnB;QAAY,CAAC,CAAC,CAChDO,IAAI,CAAEa,IAAI,IAAK;UACd,IAAIlB,YAAY,CAACI,OAAO,EAAE;YACxBL,cAAc,CAAEoB,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;YAC5B1B,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE,GAAG0B,IAAI,CAAC,CAAC;UACnC;QACF,CAAC,CAAC,CACDX,OAAO,CAAC,MAAM;UACb,IAAIP,YAAY,CAACI,OAAO,EAAE;YACxBT,UAAU,CAAC,KAAK,CAAC;UACnB;QACF,CAAC,CAAC;MACN;IACF;EACF,CAAC;EAED,oBACEb,OAAA,CAACH,MAAM;IACLyC,UAAU;IACVC,UAAU;IACVC,aAAa,EAAEX,QAAS;IACxBY,YAAY,EAAE,IAAK;IACnBC,YAAY,EAAE,KAAM;IACpBC,QAAQ,EAAExB,eAAgB;IAC1ByB,eAAe,EAAEpC,QAAQ,gBAAGR,OAAA,CAACF,IAAI;MAAC+C,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,YAAa;IACjEC,OAAO,EAAEvB,YAAa;IAAA,GAClBrB,KAAK;IAAA6C,QAAA,GAERzC,OAAO,CAAC0C,GAAG,CAAC,CAAChB,IAAI,EAAEiB,KAAK,kBACvBrD,OAAA,CAACH,MAAM,CAACyD,MAAM;MAAajC,KAAK,EAAEe,IAAI,CAACf,KAAM;MAAA8B,QAAA,EAC1Cf,IAAI,CAACmB;IAAK,GADOF,KAAK;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CAChB,CAAC,EACDrC,OAAO,iBACNZ,OAAA,CAACH,MAAM,CAACyD,MAAM;MAAAH,QAAA,eACZnD,OAAA,CAACF,IAAI;QAAC+C,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAChB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC1C,EAAA,CAxGWN,cAAc;AAAAuD,EAAA,GAAdvD,cAAc;AAAA,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}