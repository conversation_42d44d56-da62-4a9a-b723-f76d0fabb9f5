{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\report-extras\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Card, Col, Row, Space, Typography, Table, Tag, Button, DatePicker, Spin } from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport SearchInput from '../../components/search-input';\nimport { CloudDownloadOutlined } from '@ant-design/icons';\nimport ReportService from '../../services/reports';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport ReportChart from '../../components/report/chart';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport FilterColumns from '../../components/filter-column';\nimport { export_url } from '../../configs/app-global';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport { clearCompare, fetchExtrasChart, fetchReportExtras, fetchExtrasList, ReportExtrasCompare } from '../../redux/slices/report/extras';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { useTranslation } from 'react-i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport { useMemo } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text,\n  Title\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst ReportExtras = () => {\n  _s();\n  var _extrasList$data, _extrasList$data2, _extrasList$data3, _extrasList$data4, _extrasList$data4$met;\n  const {\n    t\n  } = useTranslation();\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const {\n    date_from,\n    date_to,\n    by_time,\n    chart,\n    handleChart,\n    handleDateRange\n  } = useContext(ReportContext);\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const {\n    loading,\n    chartData: reportData,\n    extrasList\n  } = useSelector(state => state.extrasReport, shallowEqual);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [downloading, setDownloading] = useState(false);\n  const [search, setSearch] = useState('');\n  const {\n    defaultCurrency\n  } = useSelector(state => state.currency, shallowEqual);\n  const expandedRowRender = row => {\n    // const data = row.stocks\n    //   .map((stock) => stock.extras)\n    //   .filter((extras) => extras.length > 0);\n\n    const columns = [{\n      title: t('extras.group'),\n      dataIndex: 'Extras Group',\n      render: (_, data) => {\n        var _data$extras, _data$extras$map;\n        return (_data$extras = data.extras) === null || _data$extras === void 0 ? void 0 : (_data$extras$map = _data$extras.map(extra => {\n          var _extra$group, _extra$group$translat;\n          return extra === null || extra === void 0 ? void 0 : (_extra$group = extra.group) === null || _extra$group === void 0 ? void 0 : (_extra$group$translat = _extra$group.translation) === null || _extra$group$translat === void 0 ? void 0 : _extra$group$translat.title;\n        })) === null || _data$extras$map === void 0 ? void 0 : _data$extras$map.join(',');\n      },\n      key: 'Extras name'\n    }, {\n      title: t('extras.value'),\n      render: (_, data) => {\n        var _data$extras2;\n        return data === null || data === void 0 ? void 0 : (_data$extras2 = data.extras) === null || _data$extras2 === void 0 ? void 0 : _data$extras2.map(extra => extra === null || extra === void 0 ? void 0 : extra.value);\n      },\n      dataIndex: 'value',\n      key: 'name'\n    }, {\n      title: t('status'),\n      dataIndex: 'active',\n      render: (_, data) => {\n        var _data$extras3;\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          children: (data === null || data === void 0 ? void 0 : (_data$extras3 = data.extras) === null || _data$extras3 === void 0 ? void 0 : _data$extras3.map(extra => extra === null || extra === void 0 ? void 0 : extra.active)[0]) === 1 ? t('active') : t('inactive')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this);\n      },\n      key: 'active'\n    }];\n    return /*#__PURE__*/_jsxDEV(Table, {\n      scroll: {\n        x: true\n      },\n      columns: columns,\n      dataSource: row === null || row === void 0 ? void 0 : row.stocks,\n      pagination: false,\n      showHeader: false,\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this);\n  };\n  const [columns, setColumns] = useState([{\n    title: t('title'),\n    dataIndex: 'translation_title',\n    key: 'translation_title',\n    render: (_, data) => {\n      return data === null || data === void 0 ? void 0 : data.translation.title;\n    },\n    is_show: true,\n    sorter: (a, b) => {\n      var _a$translation, _b$translation;\n      return a === null || a === void 0 ? void 0 : (_a$translation = a.translation) === null || _a$translation === void 0 ? void 0 : _a$translation.title.localeCompare(b === null || b === void 0 ? void 0 : (_b$translation = b.translation) === null || _b$translation === void 0 ? void 0 : _b$translation.title);\n    }\n  }, {\n    title: t('bar.code'),\n    dataIndex: 'bar_code',\n    key: 'bar_code',\n    is_show: true,\n    render: (_, data) => {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (data === null || data === void 0 ? void 0 : data.bar_code) || '-'\n      }, void 0, false);\n    }\n  }, {\n    title: t('price'),\n    dataIndex: 'price',\n    key: 'price',\n    is_show: true,\n    render: (_, data) => numberToPrice(data === null || data === void 0 ? void 0 : data.price, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position),\n    sorter: (a, b) => a.price - b.price\n  }, {\n    title: t('quantity'),\n    key: 'quantity',\n    dataIndex: 'quantity',\n    is_show: true,\n    sorter: (a, b) => a.quantity - b.quantity\n  }, {\n    title: t('status'),\n    key: 'active',\n    dataIndex: 'active',\n    render: (_, data) => {\n      const status = Boolean(data === null || data === void 0 ? void 0 : data.active);\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: status ? 'green' : 'red',\n        children: status ? t('active') : t('inactive')\n      }, data.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this);\n    },\n    is_show: true\n  }\n  // {\n  //   title: t('stock'),\n  //   key: 'stocks_total',\n  //   dataIndex: 'stocks_total',\n  //   is_show: true,\n  //   render: (_, data) => {\n  //     return data.stocks?.map((stock) => stock.quantity);\n  //   },\n  // },\n  ]);\n  const chart_type = useMemo(() => [{\n    label: 'item.sold',\n    value: 'avg_quantity',\n    qty: 'quantity',\n    price: false\n  }, {\n    label: 'net.sales',\n    value: 'price',\n    qty: 'price',\n    price: true\n  }, {\n    label: t('orders'),\n    value: 'count',\n    qty: 'count',\n    price: false\n  }], []);\n  const fetchReport = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    dispatch(fetchExtrasChart(params));\n  };\n  const fetchExtras = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n      search: search || null\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    dispatch(fetchExtrasList(params));\n  };\n  useEffect(() => {\n    handleChart(chart_type[0].value);\n  }, []);\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchExtras();\n      fetchReport();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n  useDidUpdate(() => {\n    fetchExtras();\n  }, [date_to, search, category_id, product_id, date_from]);\n  useDidUpdate(() => {\n    fetchReport();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n  const onChangePagination = pagination => {\n    const {\n      pageSize: perPage,\n      current: page\n    } = pagination;\n    fetchExtras(page, perPage);\n  };\n  const excelExport = () => {\n    setDownloading(true);\n    ReportService.getReportProductList({\n      date_from,\n      date_to,\n      type: by_time,\n      export: 'excel',\n      products: rowSelection === null || rowSelection === void 0 ? void 0 : rowSelection.selectedRowKeys\n    }).then(res => {\n      const body = res.data.link;\n      if (body) {\n        window.location.href = body;\n      }\n    }).finally(() => setDownloading(false));\n  };\n  const onSelectChange = newSelectedRowKeys => {\n    setSelectedRowKeys(newSelectedRowKeys);\n  };\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: onSelectChange\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    size: \"large\",\n    spinning: loading,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            ...configureRangePicker(),\n            defaultValue: [moment(date_from), moment(date_to)],\n            onChange: handleDateRange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"report-products\",\n      children: chart_type === null || chart_type === void 0 ? void 0 : chart_type.map(item => /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        onClick: () => handleChart(item.value),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: chart === item.value && 'active',\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-5\",\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                children: t(item.label)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 24,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Title, {\n                level: 2,\n                children: !item.price ? reportData[item.qty] : numberToPrice(reportData[item.qty], defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              className: \"d-flex justify-content-end\",\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"geekblue\",\n                className: \"d-flex align-items-center\",\n                children: \"5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)\n      }, item.label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ReportChart, {\n      reportData: reportData,\n      chart_data: \"quantities_sum\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        className: \"d-flex justify-content-between align-center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n          strong: true,\n          level: 3,\n          children: t('extras')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          className: \"d-flex justify-content-between\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(CloudDownloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 21\n            }, this),\n            loading: downloading,\n            onClick: excelExport,\n            children: t('download')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FilterColumns, {\n            columns: columns,\n            setColumns: setColumns\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        expandable: {\n          expandedRowRender,\n          defaultExpandedRowKeys: ['0']\n        },\n        rowSelection: rowSelection,\n        columns: columns === null || columns === void 0 ? void 0 : columns.filter(item => item.is_show),\n        dataSource: extrasList === null || extrasList === void 0 ? void 0 : (_extrasList$data = extrasList.data) === null || _extrasList$data === void 0 ? void 0 : _extrasList$data.data,\n        rowKey: row => row.id,\n        loading: loading,\n        pagination: {\n          pageSize: extrasList === null || extrasList === void 0 ? void 0 : (_extrasList$data2 = extrasList.data) === null || _extrasList$data2 === void 0 ? void 0 : _extrasList$data2.per_page,\n          page: (extrasList === null || extrasList === void 0 ? void 0 : (_extrasList$data3 = extrasList.data) === null || _extrasList$data3 === void 0 ? void 0 : _extrasList$data3.current_page) || 1,\n          total: extrasList === null || extrasList === void 0 ? void 0 : (_extrasList$data4 = extrasList.data) === null || _extrasList$data4 === void 0 ? void 0 : (_extrasList$data4$met = _extrasList$data4.meta) === null || _extrasList$data4$met === void 0 ? void 0 : _extrasList$data4$met.total,\n          defaultCurrent: 1\n        },\n        onChange: onChangePagination,\n        scroll: {\n          x: 1500\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportExtras, \"DvmX8QFsqB81G76hIs3JVkDkFCY=\", false, function () {\n  return [useTranslation, useDispatch, useLocation, useSelector, useSelector, useSelector, useDidUpdate, useDidUpdate];\n});\n_c = ReportExtras;\nexport default ReportExtras;\nvar _c;\n$RefreshReg$(_c, \"ReportExtras\");", "map": {"version": 3, "names": ["Card", "Col", "Row", "Space", "Typography", "Table", "Tag", "<PERSON><PERSON>", "DatePicker", "Spin", "React", "useContext", "useEffect", "useState", "SearchInput", "CloudDownloadOutlined", "ReportService", "disable<PERSON><PERSON><PERSON><PERSON>", "shallowEqual", "useDispatch", "useSelector", "ReportChart", "moment", "ReportContext", "FilterColumns", "export_url", "configureRangePicker", "clearCompare", "fetchExtrasChart", "fetchReportExtras", "fetchExtrasList", "ReportExtrasCompare", "useDidUpdate", "Link", "useLocation", "useNavigate", "QueryString", "useTranslation", "numberToPrice", "useMemo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "Title", "RangePicker", "ReportExtras", "_s", "_extrasList$data", "_extrasList$data2", "_extrasList$data3", "_extrasList$data4", "_extrasList$data4$met", "t", "dispatch", "location", "category_id", "parse", "search", "product_id", "date_from", "date_to", "by_time", "chart", "handleChart", "handleDateRange", "activeMenu", "state", "menu", "loading", "chartData", "reportData", "extrasList", "extrasReport", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "downloading", "setDownloading", "setSearch", "defaultCurrency", "currency", "expandedRowRender", "row", "columns", "title", "dataIndex", "render", "_", "data", "_data$extras", "_data$extras$map", "extras", "map", "extra", "_extra$group", "_extra$group$translat", "group", "translation", "join", "key", "_data$extras2", "value", "_data$extras3", "children", "active", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scroll", "x", "dataSource", "stocks", "pagination", "showHeader", "size", "setColumns", "is_show", "sorter", "a", "b", "_a$translation", "_b$translation", "localeCompare", "bar_code", "price", "symbol", "position", "quantity", "status", "Boolean", "color", "id", "chart_type", "label", "qty", "fetchReport", "params", "type", "categories", "products", "fetchExtras", "page", "perPage", "refetch", "onChangePagination", "pageSize", "current", "excelExport", "getReportProductList", "export", "rowSelection", "then", "res", "body", "link", "window", "href", "finally", "onSelectChange", "newSelectedRowKeys", "onChange", "spinning", "gutter", "className", "span", "defaultValue", "item", "onClick", "level", "chart_data", "strong", "icon", "expandable", "defaultExpandedRowKeys", "filter", "<PERSON><PERSON><PERSON>", "per_page", "current_page", "total", "meta", "defaultCurrent", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/report-extras/index.js"], "sourcesContent": ["import {\n  <PERSON>,\n  Col,\n  Row,\n  Space,\n  Typography,\n  Table,\n  Tag,\n  <PERSON><PERSON>,\n  DatePicker,\n  Spin,\n} from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport SearchInput from '../../components/search-input';\nimport { CloudDownloadOutlined } from '@ant-design/icons';\nimport ReportService from '../../services/reports';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport ReportChart from '../../components/report/chart';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport FilterColumns from '../../components/filter-column';\nimport { export_url } from '../../configs/app-global';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport {\n  clearCompare,\n  fetchExtrasChart,\n  fetchReportExtras,\n  fetchExtrasList,\n  ReportExtrasCompare,\n} from '../../redux/slices/report/extras';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { useTranslation } from 'react-i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport { useMemo } from 'react';\nconst { Text, Title } = Typography;\nconst { RangePicker } = DatePicker;\n\nconst ReportExtras = () => {\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const { date_from, date_to, by_time, chart, handleChart, handleDateRange } =\n    useContext(ReportContext);\n\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n\n  const {\n    loading,\n    chartData: reportData,\n    extrasList,\n  } = useSelector((state) => state.extrasReport, shallowEqual);\n\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [downloading, setDownloading] = useState(false);\n  const [search, setSearch] = useState('');\n  const { defaultCurrency } = useSelector(\n    (state) => state.currency,\n    shallowEqual,\n  );\n\n  const expandedRowRender = (row) => {\n    // const data = row.stocks\n    //   .map((stock) => stock.extras)\n    //   .filter((extras) => extras.length > 0);\n\n    const columns = [\n      {\n        title: t('extras.group'),\n        dataIndex: 'Extras Group',\n        render: (_, data) =>\n          data.extras\n            ?.map((extra) => extra?.group?.translation?.title)\n            ?.join(','),\n        key: 'Extras name',\n      },\n      {\n        title: t('extras.value'),\n        render: (_, data) => data?.extras?.map((extra) => extra?.value),\n        dataIndex: 'value',\n        key: 'name',\n      },\n      {\n        title: t('status'),\n        dataIndex: 'active',\n        render: (_, data) => {\n          return (\n            <Tag>\n              {data?.extras?.map((extra) => extra?.active)[0] === 1\n                ? t('active')\n                : t('inactive')}\n            </Tag>\n          );\n        },\n        key: 'active',\n      },\n    ];\n    return (\n      <Table\n        scroll={{ x: true }}\n        columns={columns}\n        dataSource={row?.stocks}\n        pagination={false}\n        showHeader={false}\n        size=\"small\"\n      />\n    );\n  };\n\n  const [columns, setColumns] = useState([\n    {\n      title: t('title'),\n      dataIndex: 'translation_title',\n      key: 'translation_title',\n      render: (_, data) => {\n        return data?.translation.title;\n      },\n      is_show: true,\n      sorter: (a, b) =>\n        a?.translation?.title.localeCompare(b?.translation?.title),\n    },\n    {\n      title: t('bar.code'),\n      dataIndex: 'bar_code',\n      key: 'bar_code',\n      is_show: true,\n      render: (_, data) => {\n        return <>{data?.bar_code || '-'}</>;\n      },\n    },\n\n    {\n      title: t('price'),\n      dataIndex: 'price',\n      key: 'price',\n      is_show: true,\n      render: (_, data) =>\n        numberToPrice(\n          data?.price,\n          defaultCurrency?.symbol,\n          defaultCurrency?.position,\n        ),\n      sorter: (a, b) => a.price - b.price,\n    },\n    {\n      title: t('quantity'),\n      key: 'quantity',\n      dataIndex: 'quantity',\n      is_show: true,\n      sorter: (a, b) => a.quantity - b.quantity,\n    },\n    {\n      title: t('status'),\n      key: 'active',\n      dataIndex: 'active',\n      render: (_, data) => {\n        const status = Boolean(data?.active);\n        return (\n          <Tag color={status ? 'green' : 'red'} key={data.id}>\n            {status ? t('active') : t('inactive')}\n          </Tag>\n        );\n      },\n      is_show: true,\n    },\n    // {\n    //   title: t('stock'),\n    //   key: 'stocks_total',\n    //   dataIndex: 'stocks_total',\n    //   is_show: true,\n    //   render: (_, data) => {\n    //     return data.stocks?.map((stock) => stock.quantity);\n    //   },\n    // },\n  ]);\n\n  const chart_type = useMemo(\n    () => [\n      {\n        label: 'item.sold',\n        value: 'avg_quantity',\n        qty: 'quantity',\n        price: false,\n      },\n      { label: 'net.sales', value: 'price', qty: 'price', price: true },\n      {\n        label: t('orders'),\n        value: 'count',\n        qty: 'count',\n        price: false,\n      },\n    ],\n    [],\n  );\n\n  const fetchReport = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart,\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    dispatch(fetchExtrasChart(params));\n  };\n\n  const fetchExtras = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n      search: search || null,\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    dispatch(fetchExtrasList(params));\n  };\n\n  useEffect(() => {\n    handleChart(chart_type[0].value);\n  }, []);\n\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchExtras();\n      fetchReport();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n\n  useDidUpdate(() => {\n    fetchExtras();\n  }, [date_to, search, category_id, product_id, date_from]);\n\n  useDidUpdate(() => {\n    fetchReport();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n\n  const onChangePagination = (pagination) => {\n    const { pageSize: perPage, current: page } = pagination;\n    fetchExtras(page, perPage);\n  };\n\n  const excelExport = () => {\n    setDownloading(true);\n    ReportService.getReportProductList({\n      date_from,\n      date_to,\n      type: by_time,\n      export: 'excel',\n      products: rowSelection?.selectedRowKeys,\n    })\n      .then((res) => {\n        const body = res.data.link;\n        if (body) {\n          window.location.href = body;\n        }\n      })\n      .finally(() => setDownloading(false));\n  };\n\n  const onSelectChange = (newSelectedRowKeys) => {\n    setSelectedRowKeys(newSelectedRowKeys);\n  };\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: onSelectChange,\n  };\n\n  return (\n    <Spin size='large' spinning={loading}>\n      <Row gutter={24} className='mb-3'>\n        <Col span={12}>\n          <Space size='large'>\n            <RangePicker\n              {...configureRangePicker()}\n              defaultValue={[moment(date_from), moment(date_to)]}\n              onChange={handleDateRange}\n            />\n          </Space>\n        </Col>\n      </Row>\n      <Row gutter={24} className='report-products'>\n        {chart_type?.map((item) => (\n          <Col\n            span={8}\n            key={item.label}\n            onClick={() => handleChart(item.value)}\n          >\n            <Card className={chart === item.value && 'active'}>\n              <Row className='mb-5'>\n                <Col>\n                  <Text>{t(item.label)}</Text>\n                </Col>\n              </Row>\n              <Row gutter={24}>\n                <Col span={12}>\n                  <Title level={2}>\n                    {!item.price\n                      ? reportData[item.qty]\n                      : numberToPrice(\n                          reportData[item.qty],\n                          defaultCurrency?.symbol,\n                          defaultCurrency?.position,\n                        )}\n                  </Title>\n                </Col>\n                <Col span={12} className='d-flex justify-content-end'>\n                  <Tag color='geekblue' className='d-flex align-items-center'>\n                    5%\n                  </Tag>\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n      <ReportChart reportData={reportData} chart_data='quantities_sum' />\n      <Card>\n        <Space className='d-flex justify-content-between align-center'>\n          <Typography.Text strong level={3}>\n            {t('extras')}\n          </Typography.Text>\n          <Space className='d-flex justify-content-between'>\n            <Button\n              icon={<CloudDownloadOutlined />}\n              loading={downloading}\n              onClick={excelExport}\n            >\n              {t('download')}\n            </Button>\n            <FilterColumns columns={columns} setColumns={setColumns} />\n          </Space>\n        </Space>\n\n        <Table\n          expandable={{\n            expandedRowRender,\n            defaultExpandedRowKeys: ['0'],\n          }}\n          rowSelection={rowSelection}\n          columns={columns?.filter((item) => item.is_show)}\n          dataSource={extrasList?.data?.data}\n          rowKey={(row) => row.id}\n          loading={loading}\n          pagination={{\n            pageSize: extrasList?.data?.per_page,\n            page: extrasList?.data?.current_page || 1,\n            total: extrasList?.data?.meta?.total,\n            defaultCurrent: 1,\n          }}\n          onChange={onChangePagination}\n          scroll={{\n            x: 1500,\n          }}\n        />\n      </Card>\n    </Spin>\n  );\n};\n\nexport default ReportExtras;\n"], "mappings": ";;AAAA,SACEA,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,IAAI,QACC,MAAM;AACb,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,OAAOC,WAAW,MAAM,+BAA+B;AACvD,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SACEC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,eAAe,EACfC,mBAAmB,QACd,kCAAkC;AACzC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,WAAW,MAAM,IAAI;AAC5B,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAChC,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAGzC,UAAU;AAClC,MAAM;EAAE0C;AAAY,CAAC,GAAGtC,UAAU;AAElC,MAAMuC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA;EACzB,MAAM;IAAEC;EAAE,CAAC,GAAGjB,cAAc,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAMuB,WAAW,GAAGrB,WAAW,CAACsB,KAAK,CAACF,QAAQ,CAACG,MAAM,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC;EAC1E,MAAMC,UAAU,GAAGxB,WAAW,CAACsB,KAAK,CAACF,QAAQ,CAACG,MAAM,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC;EACxE,MAAM;IAAEE,SAAS;IAAEC,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAgB,CAAC,GACxEvD,UAAU,CAACY,aAAa,CAAC;EAE3B,MAAM;IAAE4C;EAAW,CAAC,GAAG/C,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEnD,YAAY,CAAC;EAEvE,MAAM;IACJoD,OAAO;IACPC,SAAS,EAAEC,UAAU;IACrBC;EACF,CAAC,GAAGrD,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACM,YAAY,EAAExD,YAAY,CAAC;EAE5D,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8C,MAAM,EAAEoB,SAAS,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM;IAAEmE;EAAgB,CAAC,GAAG5D,WAAW,CACpCgD,KAAK,IAAKA,KAAK,CAACa,QAAQ,EACzB/D,YACF,CAAC;EAED,MAAMgE,iBAAiB,GAAIC,GAAG,IAAK;IACjC;IACA;IACA;;IAEA,MAAMC,OAAO,GAAG,CACd;MACEC,KAAK,EAAE/B,CAAC,CAAC,cAAc,CAAC;MACxBgC,SAAS,EAAE,cAAc;MACzBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI;QAAA,IAAAC,YAAA,EAAAC,gBAAA;QAAA,QAAAD,YAAA,GACdD,IAAI,CAACG,MAAM,cAAAF,YAAA,wBAAAC,gBAAA,GAAXD,YAAA,CACIG,GAAG,CAAEC,KAAK;UAAA,IAAAC,YAAA,EAAAC,qBAAA;UAAA,OAAKF,KAAK,aAALA,KAAK,wBAAAC,YAAA,GAALD,KAAK,CAAEG,KAAK,cAAAF,YAAA,wBAAAC,qBAAA,GAAZD,YAAA,CAAcG,WAAW,cAAAF,qBAAA,uBAAzBA,qBAAA,CAA2BX,KAAK;QAAA,EAAC,cAAAM,gBAAA,uBADpDA,gBAAA,CAEIQ,IAAI,CAAC,GAAG,CAAC;MAAA;MACfC,GAAG,EAAE;IACP,CAAC,EACD;MACEf,KAAK,EAAE/B,CAAC,CAAC,cAAc,CAAC;MACxBiC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI;QAAA,IAAAY,aAAA;QAAA,OAAKZ,IAAI,aAAJA,IAAI,wBAAAY,aAAA,GAAJZ,IAAI,CAAEG,MAAM,cAAAS,aAAA,uBAAZA,aAAA,CAAcR,GAAG,CAAEC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,KAAK,CAAC;MAAA;MAC/DhB,SAAS,EAAE,OAAO;MAClBc,GAAG,EAAE;IACP,CAAC,EACD;MACEf,KAAK,EAAE/B,CAAC,CAAC,QAAQ,CAAC;MAClBgC,SAAS,EAAE,QAAQ;MACnBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;QAAA,IAAAc,aAAA;QACnB,oBACE9D,OAAA,CAACnC,GAAG;UAAAkG,QAAA,EACD,CAAAf,IAAI,aAAJA,IAAI,wBAAAc,aAAA,GAAJd,IAAI,CAAEG,MAAM,cAAAW,aAAA,uBAAZA,aAAA,CAAcV,GAAG,CAAEC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,MAAM,CAAC,CAAC,CAAC,CAAC,MAAK,CAAC,GACjDnD,CAAC,CAAC,QAAQ,CAAC,GACXA,CAAC,CAAC,UAAU;QAAC;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAEV,CAAC;MACDT,GAAG,EAAE;IACP,CAAC,CACF;IACD,oBACE3D,OAAA,CAACpC,KAAK;MACJyG,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK,CAAE;MACpB3B,OAAO,EAAEA,OAAQ;MACjB4B,UAAU,EAAE7B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE8B,MAAO;MACxBC,UAAU,EAAE,KAAM;MAClBC,UAAU,EAAE,KAAM;MAClBC,IAAI,EAAC;IAAO;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEN,CAAC;EAED,MAAM,CAACzB,OAAO,EAAEiC,UAAU,CAAC,GAAGxG,QAAQ,CAAC,CACrC;IACEwE,KAAK,EAAE/B,CAAC,CAAC,OAAO,CAAC;IACjBgC,SAAS,EAAE,mBAAmB;IAC9Bc,GAAG,EAAE,mBAAmB;IACxBb,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,WAAW,CAACb,KAAK;IAChC,CAAC;IACDiC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC;MAAA,IAAAC,cAAA,EAAAC,cAAA;MAAA,OACXH,CAAC,aAADA,CAAC,wBAAAE,cAAA,GAADF,CAAC,CAAEtB,WAAW,cAAAwB,cAAA,uBAAdA,cAAA,CAAgBrC,KAAK,CAACuC,aAAa,CAACH,CAAC,aAADA,CAAC,wBAAAE,cAAA,GAADF,CAAC,CAAEvB,WAAW,cAAAyB,cAAA,uBAAdA,cAAA,CAAgBtC,KAAK,CAAC;IAAA;EAC9D,CAAC,EACD;IACEA,KAAK,EAAE/B,CAAC,CAAC,UAAU,CAAC;IACpBgC,SAAS,EAAE,UAAU;IACrBc,GAAG,EAAE,UAAU;IACfkB,OAAO,EAAE,IAAI;IACb/B,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,oBAAOhD,OAAA,CAAAE,SAAA;QAAA6D,QAAA,EAAG,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,QAAQ,KAAI;MAAG,gBAAG,CAAC;IACrC;EACF,CAAC,EAED;IACExC,KAAK,EAAE/B,CAAC,CAAC,OAAO,CAAC;IACjBgC,SAAS,EAAE,OAAO;IAClBc,GAAG,EAAE,OAAO;IACZkB,OAAO,EAAE,IAAI;IACb/B,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KACdnD,aAAa,CACXmD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,KAAK,EACX9C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+C,MAAM,EACvB/C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgD,QACnB,CAAC;IACHT,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACM,KAAK,GAAGL,CAAC,CAACK;EAChC,CAAC,EACD;IACEzC,KAAK,EAAE/B,CAAC,CAAC,UAAU,CAAC;IACpB8C,GAAG,EAAE,UAAU;IACfd,SAAS,EAAE,UAAU;IACrBgC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACS,QAAQ,GAAGR,CAAC,CAACQ;EACnC,CAAC,EACD;IACE5C,KAAK,EAAE/B,CAAC,CAAC,QAAQ,CAAC;IAClB8C,GAAG,EAAE,QAAQ;IACbd,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,MAAMyC,MAAM,GAAGC,OAAO,CAAC1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,MAAM,CAAC;MACpC,oBACEhE,OAAA,CAACnC,GAAG;QAAC8H,KAAK,EAAEF,MAAM,GAAG,OAAO,GAAG,KAAM;QAAA1B,QAAA,EAClC0B,MAAM,GAAG5E,CAAC,CAAC,QAAQ,CAAC,GAAGA,CAAC,CAAC,UAAU;MAAC,GADImC,IAAI,CAAC4C,EAAE;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7C,CAAC;IAEV,CAAC;IACDS,OAAO,EAAE;EACX;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA,CACD,CAAC;EAEF,MAAMgB,UAAU,GAAG/F,OAAO,CACxB,MAAM,CACJ;IACEgG,KAAK,EAAE,WAAW;IAClBjC,KAAK,EAAE,cAAc;IACrBkC,GAAG,EAAE,UAAU;IACfV,KAAK,EAAE;EACT,CAAC,EACD;IAAES,KAAK,EAAE,WAAW;IAAEjC,KAAK,EAAE,OAAO;IAAEkC,GAAG,EAAE,OAAO;IAAEV,KAAK,EAAE;EAAK,CAAC,EACjE;IACES,KAAK,EAAEjF,CAAC,CAAC,QAAQ,CAAC;IAClBgD,KAAK,EAAE,OAAO;IACdkC,GAAG,EAAE,OAAO;IACZV,KAAK,EAAE;EACT,CAAC,CACF,EACD,EACF,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,MAAM,GAAG;MACb7E,SAAS;MACTC,OAAO;MACP6E,IAAI,EAAE5E,OAAO;MACbC;IACF,CAAC;IACD,IAAIP,WAAW,EAAEiF,MAAM,CAACE,UAAU,GAAG,CAACnF,WAAW,CAAC;IAClD,IAAIG,UAAU,EAAE8E,MAAM,CAACG,QAAQ,GAAG,CAACjF,UAAU,CAAC;IAC9CL,QAAQ,CAAC3B,gBAAgB,CAAC8G,MAAM,CAAC,CAAC;EACpC,CAAC;EAED,MAAMI,WAAW,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAK;IACrC,MAAMN,MAAM,GAAG;MACb7E,SAAS;MACTC,OAAO;MACP6E,IAAI,EAAE5E,OAAO;MACbgF,IAAI;MACJC,OAAO;MACPrF,MAAM,EAAEA,MAAM,IAAI;IACpB,CAAC;IACD,IAAIF,WAAW,EAAEiF,MAAM,CAACE,UAAU,GAAG,CAACnF,WAAW,CAAC;IAClD,IAAIG,UAAU,EAAE8E,MAAM,CAACG,QAAQ,GAAG,CAACjF,UAAU,CAAC;IAC9CL,QAAQ,CAACzB,eAAe,CAAC4G,MAAM,CAAC,CAAC;EACnC,CAAC;EAED9H,SAAS,CAAC,MAAM;IACdqD,WAAW,CAACqE,UAAU,CAAC,CAAC,CAAC,CAAChC,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN1F,SAAS,CAAC,MAAM;IACd,IAAIuD,UAAU,CAAC8E,OAAO,EAAE;MACtBH,WAAW,CAAC,CAAC;MACbL,WAAW,CAAC,CAAC;MACblF,QAAQ,CAACtC,cAAc,CAACkD,UAAU,CAAC,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,UAAU,CAAC8E,OAAO,CAAC,CAAC;EAExBjH,YAAY,CAAC,MAAM;IACjB8G,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAChF,OAAO,EAAEH,MAAM,EAAEF,WAAW,EAAEG,UAAU,EAAEC,SAAS,CAAC,CAAC;EAEzD7B,YAAY,CAAC,MAAM;IACjByG,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC3E,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEP,WAAW,EAAEG,UAAU,EAAEC,SAAS,CAAC,CAAC;EAEjE,MAAMqF,kBAAkB,GAAIhC,UAAU,IAAK;IACzC,MAAM;MAAEiC,QAAQ,EAAEH,OAAO;MAAEI,OAAO,EAAEL;IAAK,CAAC,GAAG7B,UAAU;IACvD4B,WAAW,CAACC,IAAI,EAAEC,OAAO,CAAC;EAC5B,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxBvE,cAAc,CAAC,IAAI,CAAC;IACpB9D,aAAa,CAACsI,oBAAoB,CAAC;MACjCzF,SAAS;MACTC,OAAO;MACP6E,IAAI,EAAE5E,OAAO;MACbwF,MAAM,EAAE,OAAO;MACfV,QAAQ,EAAEW,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE7E;IAC1B,CAAC,CAAC,CACC8E,IAAI,CAAEC,GAAG,IAAK;MACb,MAAMC,IAAI,GAAGD,GAAG,CAACjE,IAAI,CAACmE,IAAI;MAC1B,IAAID,IAAI,EAAE;QACRE,MAAM,CAACrG,QAAQ,CAACsG,IAAI,GAAGH,IAAI;MAC7B;IACF,CAAC,CAAC,CACDI,OAAO,CAAC,MAAMjF,cAAc,CAAC,KAAK,CAAC,CAAC;EACzC,CAAC;EAED,MAAMkF,cAAc,GAAIC,kBAAkB,IAAK;IAC7CrF,kBAAkB,CAACqF,kBAAkB,CAAC;EACxC,CAAC;EAED,MAAMT,YAAY,GAAG;IACnB7E,eAAe;IACfuF,QAAQ,EAAEF;EACZ,CAAC;EAED,oBACEvH,OAAA,CAAChC,IAAI;IAAC2G,IAAI,EAAC,OAAO;IAAC+C,QAAQ,EAAE7F,OAAQ;IAAAkC,QAAA,gBACnC/D,OAAA,CAACvC,GAAG;MAACkK,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,MAAM;MAAA7D,QAAA,eAC/B/D,OAAA,CAACxC,GAAG;QAACqK,IAAI,EAAE,EAAG;QAAA9D,QAAA,eACZ/D,OAAA,CAACtC,KAAK;UAACiH,IAAI,EAAC,OAAO;UAAAZ,QAAA,eACjB/D,OAAA,CAACK,WAAW;YAAA,GACNpB,oBAAoB,CAAC,CAAC;YAC1B6I,YAAY,EAAE,CAACjJ,MAAM,CAACuC,SAAS,CAAC,EAAEvC,MAAM,CAACwC,OAAO,CAAC,CAAE;YACnDoG,QAAQ,EAAEhG;UAAgB;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNpE,OAAA,CAACvC,GAAG;MAACkK,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,iBAAiB;MAAA7D,QAAA,EACzC8B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzC,GAAG,CAAE2E,IAAI,iBACpB/H,OAAA,CAACxC,GAAG;QACFqK,IAAI,EAAE,CAAE;QAERG,OAAO,EAAEA,CAAA,KAAMxG,WAAW,CAACuG,IAAI,CAAClE,KAAK,CAAE;QAAAE,QAAA,eAEvC/D,OAAA,CAACzC,IAAI;UAACqK,SAAS,EAAErG,KAAK,KAAKwG,IAAI,CAAClE,KAAK,IAAI,QAAS;UAAAE,QAAA,gBAChD/D,OAAA,CAACvC,GAAG;YAACmK,SAAS,EAAC,MAAM;YAAA7D,QAAA,eACnB/D,OAAA,CAACxC,GAAG;cAAAuG,QAAA,eACF/D,OAAA,CAACG,IAAI;gBAAA4D,QAAA,EAAElD,CAAC,CAACkH,IAAI,CAACjC,KAAK;cAAC;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpE,OAAA,CAACvC,GAAG;YAACkK,MAAM,EAAE,EAAG;YAAA5D,QAAA,gBACd/D,OAAA,CAACxC,GAAG;cAACqK,IAAI,EAAE,EAAG;cAAA9D,QAAA,eACZ/D,OAAA,CAACI,KAAK;gBAAC6H,KAAK,EAAE,CAAE;gBAAAlE,QAAA,EACb,CAACgE,IAAI,CAAC1C,KAAK,GACRtD,UAAU,CAACgG,IAAI,CAAChC,GAAG,CAAC,GACpBlG,aAAa,CACXkC,UAAU,CAACgG,IAAI,CAAChC,GAAG,CAAC,EACpBxD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+C,MAAM,EACvB/C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgD,QACnB;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNpE,OAAA,CAACxC,GAAG;cAACqK,IAAI,EAAE,EAAG;cAACD,SAAS,EAAC,4BAA4B;cAAA7D,QAAA,eACnD/D,OAAA,CAACnC,GAAG;gBAAC8H,KAAK,EAAC,UAAU;gBAACiC,SAAS,EAAC,2BAA2B;gBAAA7D,QAAA,EAAC;cAE5D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GA3BF2D,IAAI,CAACjC,KAAK;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4BZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNpE,OAAA,CAACpB,WAAW;MAACmD,UAAU,EAAEA,UAAW;MAACmG,UAAU,EAAC;IAAgB;MAAAjE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnEpE,OAAA,CAACzC,IAAI;MAAAwG,QAAA,gBACH/D,OAAA,CAACtC,KAAK;QAACkK,SAAS,EAAC,6CAA6C;QAAA7D,QAAA,gBAC5D/D,OAAA,CAACrC,UAAU,CAACwC,IAAI;UAACgI,MAAM;UAACF,KAAK,EAAE,CAAE;UAAAlE,QAAA,EAC9BlD,CAAC,CAAC,QAAQ;QAAC;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAClBpE,OAAA,CAACtC,KAAK;UAACkK,SAAS,EAAC,gCAAgC;UAAA7D,QAAA,gBAC/C/D,OAAA,CAAClC,MAAM;YACLsK,IAAI,eAAEpI,OAAA,CAAC1B,qBAAqB;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCvC,OAAO,EAAEO,WAAY;YACrB4F,OAAO,EAAEpB,WAAY;YAAA7C,QAAA,EAEpBlD,CAAC,CAAC,UAAU;UAAC;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACTpE,OAAA,CAACjB,aAAa;YAAC4D,OAAO,EAAEA,OAAQ;YAACiC,UAAU,EAAEA;UAAW;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAERpE,OAAA,CAACpC,KAAK;QACJyK,UAAU,EAAE;UACV5F,iBAAiB;UACjB6F,sBAAsB,EAAE,CAAC,GAAG;QAC9B,CAAE;QACFvB,YAAY,EAAEA,YAAa;QAC3BpE,OAAO,EAAEA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4F,MAAM,CAAER,IAAI,IAAKA,IAAI,CAAClD,OAAO,CAAE;QACjDN,UAAU,EAAEvC,UAAU,aAAVA,UAAU,wBAAAxB,gBAAA,GAAVwB,UAAU,CAAEgB,IAAI,cAAAxC,gBAAA,uBAAhBA,gBAAA,CAAkBwC,IAAK;QACnCwF,MAAM,EAAG9F,GAAG,IAAKA,GAAG,CAACkD,EAAG;QACxB/D,OAAO,EAAEA,OAAQ;QACjB4C,UAAU,EAAE;UACViC,QAAQ,EAAE1E,UAAU,aAAVA,UAAU,wBAAAvB,iBAAA,GAAVuB,UAAU,CAAEgB,IAAI,cAAAvC,iBAAA,uBAAhBA,iBAAA,CAAkBgI,QAAQ;UACpCnC,IAAI,EAAE,CAAAtE,UAAU,aAAVA,UAAU,wBAAAtB,iBAAA,GAAVsB,UAAU,CAAEgB,IAAI,cAAAtC,iBAAA,uBAAhBA,iBAAA,CAAkBgI,YAAY,KAAI,CAAC;UACzCC,KAAK,EAAE3G,UAAU,aAAVA,UAAU,wBAAArB,iBAAA,GAAVqB,UAAU,CAAEgB,IAAI,cAAArC,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBiI,IAAI,cAAAhI,qBAAA,uBAAtBA,qBAAA,CAAwB+H,KAAK;UACpCE,cAAc,EAAE;QAClB,CAAE;QACFpB,QAAQ,EAAEhB,kBAAmB;QAC7BpC,MAAM,EAAE;UACNC,CAAC,EAAE;QACL;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAAC7D,EAAA,CAvUID,YAAY;EAAA,QACFV,cAAc,EACXlB,WAAW,EACXe,WAAW,EAMLd,WAAW,EAM9BA,WAAW,EAKaA,WAAW,EAiLvCY,YAAY,EAIZA,YAAY;AAAA;AAAAuJ,EAAA,GAzMRxI,YAAY;AAyUlB,eAAeA,YAAY;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}