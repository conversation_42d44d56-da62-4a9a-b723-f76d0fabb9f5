{"__meta": {"id": "X0b9b5bb06d7c76372ca48d87608be976", "datetime": "2025-07-21 20:15:21", "utime": 1753139721.092209, "method": "POST", "uri": "/api/v1/dashboard/user/profile/firebase/token/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[20:15:21] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753139721.016578, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753139720.81048, "end": 1753139721.092226, "duration": 0.28174591064453125, "duration_str": "282ms", "measures": [{"label": "Booting", "start": 1753139720.81048, "relative_start": 0, "end": 1753139721.001641, "relative_end": 1753139721.001641, "duration": 0.1911609172821045, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753139721.00165, "relative_start": 0.19116997718811035, "end": 1753139721.092228, "relative_end": 1.9073486328125e-06, "duration": 0.09057784080505371, "duration_str": "90.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 40310528, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/user/profile/firebase/token/update", "middleware": "api, block.ip, sanctum.check", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController@fireBaseTokenUpdate", "as": "user.", "namespace": null, "prefix": "api/v1/dashboard/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php&line=139\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php:139-164</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.01585, "accumulated_duration_str": "15.85ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00268, "duration_str": "2.68ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 16.909}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 16.909, "width_percent": 2.082}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 18.991, "width_percent": 1.956}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 20.946, "width_percent": 2.019}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '60' limit 1", "type": "query", "params": [], "bindings": ["60"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 22.965, "width_percent": 3.281}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 26.246, "width_percent": 3.155}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-21 20:15:21', `personal_access_tokens`.`updated_at` = '2025-07-21 20:15:21' where `id` = 60", "type": "query", "params": [], "bindings": ["2025-07-21 20:15:21", "2025-07-21 20:15:21", "60"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 29.401, "width_percent": 8.896}, {"sql": "select * from `users` where `uuid` = 'b4deae0d-fecb-4fd8-a244-c4a2fe32f2f0' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b4deae0d-fecb-4fd8-a244-c4a2fe32f2f0"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 145}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php:145", "connection": "foodyman", "start_percent": 38.297, "width_percent": 2.271}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'users'", "type": "query", "params": [], "bindings": ["foodyman", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 157}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php:157", "connection": "foodyman", "start_percent": 40.568, "width_percent": 6.94}, {"sql": "update `users` set `firebase_token` = '[\\\"eScsKjJzGjO5cBX_P6BoD8:APA91bFgwIkbcVHE9uUo8a40dTrq02s4ajvEnHJXRsQRIzqiZ2QVz37PEt1d7t_-vgccvvLDV3S1Q5kD261ZiLn0syHQWNtq5glKT75eWtB5dTqBq1FCWqk\\\",\\\"fG1TjZuLX1Fm4Krsby0Oa0:APA91bE2tLje2WBNhykCd99eRf9XLf5YXjg8Njg1gAy4Fj6z4yXiye7Ta5CU5MKWk455Ycpn1MzxM9onDf8Eqcb8J-TbvA6bKsKQRvznSIVBEbgWiC7gEE4\\\",\\\"e3Xu5GMVPOsYzpe-yy6Odl:APA91bGOtv6swLQJENQDpt1BdRkFxzQT_OyT--f4m_z30Qs_QvMZ7yw_X-LaAOTkfj6CN3I3LSI4RSjN-7DhvD0LlYbKhGViZEQkjckxsj-lJIQUSB_LXK0\\\",\\\"d3MaTWtjAmZYt-b47x5Ey0:APA91bEoKfumqFlNEAXBjPhTJdOBzddWz4DVPxZY_hfgFcmzYfaLhzhcHmYCdy_p39mpoUjadKB1G3Q7tD-QcyrrRvyBWAfG_qQiKX8VFNj8BuS6gkKcsT4\\\",\\\"f5-22sIRSkSDuH27bmOYQk:APA91bFsQsL31RyWKiBlzr3NOI8quLbI-kH4rCTYEabc0KdqclttCLd9c4CspXm7d6NhD83V4qPw0Z577qRfjJ9yP9Wll2TiKsTlc4OCMmmAwqtjEg4_t_A\\\",\\\"dmZqIMJeIa2SalIJB8izgV:APA91bFjqSNb3U86toipnosh291ovt-fLDlZucTBfgpPsgXdKRnNM4lekYNB7ir7-BbZbsbzS2_DaXv1dbqNPZ8z7TJ6nocLOVv7jS5Rogw50amMWIhy0EY\\\",\\\"dAEmiMMREA7IIR_AEM8d4X:APA91bEvGV1nwVWxxs1Pyae0PXcPJyVkmesRWa5kzZh-XjFR3zmNRgu8KbekpiODL7hZcDVI7LuZhNejrfXu4bA3MuEBZFfJOl80daDASdNQA87av4M-TKk\\\",\\\"eVfXF9-rpqO_skEsPW9Xrt:APA91bEp-Jm4Xr_LauNVQ0PDBdDpIb3YPXmcnEyJBYjWb78K0tDvFkDvJLgtc3ZUMVjojjUy2lPXJVvS4pjM08Eqpjt4LrByrzssBaE8B9aILeTLrC-oHrU\\\",\\\"cY-v5gZveKVCTEmkABMyAP:APA91bHzovDtt5QfN1UX_72tOi-UZft1mdxKwd8UFE9dLRFoifZyjQQtK3_spM3hLX-PyzDR4VoJ80aaiJMIcsCIQCrWuh3kY2QVrWQcUxuvjTazVNR4uDo\\\",\\\"dqk0tVQuQ9uRyYiU3FqkC4:APA91bEeCo5VX3mhfB4djQWaLBtT-hGiG9tonWjBzuX44W4f5oW37sV7rA22Vtc17WRXBc_MbOKxtr5iIHg03MLkBDXHL_chuBr6FiHuIloyAERtOQYSWdA\\\",\\\"eARKkW935slJb0V9pVqxsw:APA91bHmn-nhRCN2_rn7kvwnhq-rgCCh57uj6-wIqpDfw7IfqNo5YpH6CpApxC2u0Tuj4U37A6FPMsprikJHi7TuIw7j_SRkdi6Gu3XqizvETln3yQT4Xns\\\",\\\"fWAcK-S3yB63gUkueA5fnw:APA91bFkDV_11QCegZ3hPWl4Db0YxG8fjM5mGhSD2jkqXWPRzeL-IDuUwM2kC24Fzi0HifdIw4bRxDnaG_hc2TYHfaOsUOZrBtsbsnyFAOY92nNSjWaWwtQ\\\",\\\"cl6yJ-EEzJ4MIsfuGYkG1b:APA91bGK42Uumm4karP_-P6qRaEOt-vlYtBhmolc2oWwBAzoIOdP7nw0NNGBwxn45WPtCc3MLmrMALafYDuyN5KWftP0S38nP_gD5xJdxIJdWKh4wrD515I\\\",\\\"c3aNdhe20-3kp7p_owkF5K:APA91bFR57VUmUmh39dNlHKw5eJv6Ob6accOpnrWzFw8578MYIGMhDGsSAWTxuWt-DprWV5WquBSxgNp896AxmEHlbmzflA-ZgPBFWKr0rEBy9JddtR2OhY\\\",\\\"dTViIlKMjZED-A4q1MDKr5:APA91bHaYFLluDAT5ziCliwBHoBvy875cLsiH669iSPspQq_dRvBM8dVYjPyb0iDsX1Xv106RGy69cNM6j9gpeIMKVT2mePoRSsnqOvT_lfnSZjCA1AsS3A\\\",\\\"e3IYg5iKxK8nAbGgtiAvOa:APA91bH9F_R-vZugO7D2mtlNwoGEMfCDRS1-7wCuFNN17wOcPTh_8VCLc58esZUTWYZyLDL0aXWX8p7JKAy-M6Ba_NXtZBNAYfYlg1RFxRDjc-vwCSrhjAI\\\",\\\"crsP87t7skv02ykw__Tiry:APA91bHHKj1Y2Quqlnn28Xsc1runWQDqzDX0-gd0Vch8N6d0nvHuJsDwy-fVWD-Ds4KdIwdNJvKYrIE_jFcLMyYenpF4qI8kns1-7JFd9W4AkphuUJKRrsE\\\",\\\"fkfH7WboiHR2__iMV_eMhr:APA91bG85Zhto_Iw0sHBIiLnwnTRz2ev6dGg_f0ydk485yuyU3ds8x-PglqJ50jfKSX4q_wFhl8c1d8-v7Cx-_4ymmT5H-bf30GBBr4PoyXSf30OXyLGpH4\\\",\\\"czShvYoPj2uhSRtRctMkvt:APA91bGzlAzreJ7qhrZdAzmdd5QYineJLoE7SriA7666wnYec5TAdHdYH7L0WaoxNJMbGi-RznCxs2aOwFPVXMnckSGVjoaRvoWxZpd13seCfEH8OPJAXOU\\\"]', `users`.`updated_at` = '2025-07-21 20:15:21' where `id` = 101", "type": "query", "params": [], "bindings": ["[&quot;eScsKjJzGjO5cBX_P6BoD8:APA91bFgwIkbcVHE9uUo8a40dTrq02s4ajvEnHJXRsQRIzqiZ2QVz37PEt1d7t_-vgccvvLDV3S1Q5kD261ZiLn0syHQWNtq5glKT75eWtB5dTqBq1FCWqk&quot;,&quot;fG1TjZuLX1Fm4Krsby0Oa0:APA91bE2tLje2WBNhykCd99eRf9XLf5YXjg8Njg1gAy4Fj6z4yXiye7Ta5CU5MKWk455Ycpn1MzxM9onDf8Eqcb8J-TbvA6bKsKQRvznSIVBEbgWiC7gEE4&quot;,&quot;e3Xu5GMVPOsYzpe-yy6Odl:APA91bGOtv6swLQJENQDpt1BdRkFxzQT_OyT--f4m_z30Qs_QvMZ7yw_X-LaAOTkfj6CN3I3LSI4RSjN-7DhvD0LlYbKhGViZEQkjckxsj-lJIQUSB_LXK0&quot;,&quot;d3MaTWtjAmZYt-b47x5Ey0:APA91bEoKfumqFlNEAXBjPhTJdOBzddWz4DVPxZY_hfgFcmzYfaLhzhcHmYCdy_p39mpoUjadKB1G3Q7tD-QcyrrRvyBWAfG_qQiKX8VFNj8BuS6gkKcsT4&quot;,&quot;f5-22sIRSkSDuH27bmOYQk:APA91bFsQsL31RyWKiBlzr3NOI8quLbI-kH4rCTYEabc0KdqclttCLd9c4CspXm7d6NhD83V4qPw0Z577qRfjJ9yP9Wll2TiKsTlc4OCMmmAwqtjEg4_t_A&quot;,&quot;dmZqIMJeIa2SalIJB8izgV:APA91bFjqSNb3U86toipnosh291ovt-fLDlZucTBfgpPsgXdKRnNM4lekYNB7ir7-BbZbsbzS2_DaXv1dbqNPZ8z7TJ6nocLOVv7jS5Rogw50amMWIhy0EY&quot;,&quot;dAEmiMMREA7IIR_AEM8d4X:APA91bEvGV1nwVWxxs1Pyae0PXcPJyVkmesRWa5kzZh-XjFR3zmNRgu8KbekpiODL7hZcDVI7LuZhNejrfXu4bA3MuEBZFfJOl80daDASdNQA87av4M-TKk&quot;,&quot;eVfXF9-rpqO_skEsPW9Xrt:APA91bEp-Jm4Xr_LauNVQ0PDBdDpIb3YPXmcnEyJBYjWb78K0tDvFkDvJLgtc3ZUMVjojjUy2lPXJVvS4pjM08Eqpjt4LrByrzssBaE8B9aILeTLrC-oHrU&quot;,&quot;cY-v5gZveKVCTEmkABMyAP:APA91bHzovDtt5QfN1UX_72tOi-UZft1mdxKwd8UFE9dLRFoifZyjQQtK3_spM3hLX-PyzDR4VoJ80aaiJMIcsCIQCrWuh3kY2QVrWQcUxuvjTazVNR4uDo&quot;,&quot;dqk0tVQuQ9uRyYiU3FqkC4:APA91bEeCo5VX3mhfB4djQWaLBtT-hGiG9tonWjBzuX44W4f5oW37sV7rA22Vtc17WRXBc_MbOKxtr5iIHg03MLkBDXHL_chuBr6FiHuIloyAERtOQYSWdA&quot;,&quot;eARKkW935slJb0V9pVqxsw:APA91bHmn-nhRCN2_rn7kvwnhq-rgCCh57uj6-wIqpDfw7IfqNo5YpH6CpApxC2u0Tuj4U37A6FPMsprikJHi7TuIw7j_SRkdi6Gu3XqizvETln3yQT4Xns&quot;,&quot;fWAcK-S3yB63gUkueA5fnw:APA91bFkDV_11QCegZ3hPWl4Db0YxG8fjM5mGhSD2jkqXWPRzeL-IDuUwM2kC24Fzi0HifdIw4bRxDnaG_hc2TYHfaOsUOZrBtsbsnyFAOY92nNSjWaWwtQ&quot;,&quot;cl6yJ-EEzJ4MIsfuGYkG1b:APA91bGK42Uumm4karP_-P6qRaEOt-vlYtBhmolc2oWwBAzoIOdP7nw0NNGBwxn45WPtCc3MLmrMALafYDuyN5KWftP0S38nP_gD5xJdxIJdWKh4wrD515I&quot;,&quot;c3aNdhe20-3kp7p_owkF5K:APA91bFR57VUmUmh39dNlHKw5eJv6Ob6accOpnrWzFw8578MYIGMhDGsSAWTxuWt-DprWV5WquBSxgNp896AxmEHlbmzflA-ZgPBFWKr0rEBy9JddtR2OhY&quot;,&quot;dTViIlKMjZED-A4q1MDKr5:APA91bHaYFLluDAT5ziCliwBHoBvy875cLsiH669iSPspQq_dRvBM8dVYjPyb0iDsX1Xv106RGy69cNM6j9gpeIMKVT2mePoRSsnqOvT_lfnSZjCA1AsS3A&quot;,&quot;e3IYg5iKxK8nAbGgtiAvOa:APA91bH9F_R-vZugO7D2mtlNwoGEMfCDRS1-7wCuFNN17wOcPTh_8VCLc58esZUTWYZyLDL0aXWX8p7JKAy-M6Ba_NXtZBNAYfYlg1RFxRDjc-vwCSrhjAI&quot;,&quot;crsP87t7skv02ykw__Tiry:APA91bHHKj1Y2Quqlnn28Xsc1runWQDqzDX0-gd0Vch8N6d0nvHuJsDwy-fVWD-Ds4KdIwdNJvKYrIE_jFcLMyYenpF4qI8kns1-7JFd9W4AkphuUJKRrsE&quot;,&quot;fkfH7WboiHR2__iMV_eMhr:APA91bG85Zhto_Iw0sHBIiLnwnTRz2ev6dGg_f0ydk485yuyU3ds8x-PglqJ50jfKSX4q_wFhl8c1d8-v7Cx-_4ymmT5H-bf30GBBr4PoyXSf30OXyLGpH4&quot;,&quot;czShvYoPj2uhSRtRctMkvt:APA91bGzlAzreJ7qhrZdAzmdd5QYineJLoE7SriA7666wnYec5TAdHdYH7L0WaoxNJMbGi-RznCxs2aOwFPVXMnckSGVjoaRvoWxZpd13seCfEH8OPJAXOU&quot;]", "2025-07-21 20:15:21", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 157}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.004719999999999999, "duration_str": "4.72ms", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php:157", "connection": "foodyman", "start_percent": 47.508, "width_percent": 29.779}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\UserObserver.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 157}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 77.287, "width_percent": 1.893}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\UserObserver.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 157}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 79.18, "width_percent": 1.577}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'model_logs'", "type": "query", "params": [], "bindings": ["foodyman", "model_logs"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 21, "namespace": null, "name": "\\app\\Observers\\UserObserver.php", "line": 58}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 157}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 80.757, "width_percent": 5.174}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\User', 101, '{\\\"birthday\\\":\\\"1991-08-10\\\",\\\"firebase_token\\\":\\\"[\\\\\"eScsKjJzGjO5cBX_P6BoD8:APA91bFgwIkbcVHE9uUo8a40dTrq02s4ajvEnHJXRsQRIzqiZ2QVz37PEt1d7t_-vgccvvLDV3S1Q5kD261ZiLn0syHQWNtq5glKT75eWtB5dTqBq1FCWqk\\\\\",\\\\\"fG1TjZuLX1Fm4Krsby0Oa0:APA91bE2tLje2WBNhykCd99eRf9XLf5YXjg8Njg1gAy4Fj6z4yXiye7Ta5CU5MKWk455Ycpn1MzxM9onDf8Eqcb8J-TbvA6bKsKQRvznSIVBEbgWiC7gEE4\\\\\",\\\\\"e3Xu5GMVPOsYzpe-yy6Odl:APA91bGOtv6swLQJENQDpt1BdRkFxzQT_OyT--f4m_z30Qs_QvMZ7yw_X-LaAOTkfj6CN3I3LSI4RSjN-7DhvD0LlYbKhGViZEQkjckxsj-lJIQUSB_LXK0\\\\\",\\\\\"d3MaTWtjAmZYt-b47x5Ey0:APA91bEoKfumqFlNEAXBjPhTJdOBzddWz4DVPxZY_hfgFcmzYfaLhzhcHmYCdy_p39mpoUjadKB1G3Q7tD-QcyrrRvyBWAfG_qQiKX8VFNj8BuS6gkKcsT4\\\\\",\\\\\"f5-22sIRSkSDuH27bmOYQk:APA91bFsQsL31RyWKiBlzr3NOI8quLbI-kH4rCTYEabc0KdqclttCLd9c4CspXm7d6NhD83V4qPw0Z577qRfjJ9yP9Wll2TiKsTlc4OCMmmAwqtjEg4_t_A\\\\\",\\\\\"dmZqIMJeIa2SalIJB8izgV:APA91bFjqSNb3U86toipnosh291ovt-fLDlZucTBfgpPsgXdKRnNM4lekYNB7ir7-BbZbsbzS2_DaXv1dbqNPZ8z7TJ6nocLOVv7jS5Rogw50amMWIhy0EY\\\\\",\\\\\"dAEmiMMREA7IIR_AEM8d4X:APA91bEvGV1nwVWxxs1Pyae0PXcPJyVkmesRWa5kzZh-XjFR3zmNRgu8KbekpiODL7hZcDVI7LuZhNejrfXu4bA3MuEBZFfJOl80daDASdNQA87av4M-TKk\\\\\",\\\\\"eVfXF9-rpqO_skEsPW9Xrt:APA91bEp-Jm4Xr_LauNVQ0PDBdDpIb3YPXmcnEyJBYjWb78K0tDvFkDvJLgtc3ZUMVjojjUy2lPXJVvS4pjM08Eqpjt4LrByrzssBaE8B9aILeTLrC-oHrU\\\\\",\\\\\"cY-v5gZveKVCTEmkABMyAP:APA91bHzovDtt5QfN1UX_72tOi-UZft1mdxKwd8UFE9dLRFoifZyjQQtK3_spM3hLX-PyzDR4VoJ80aaiJMIcsCIQCrWuh3kY2QVrWQcUxuvjTazVNR4uDo\\\\\",\\\\\"dqk0tVQuQ9uRyYiU3FqkC4:APA91bEeCo5VX3mhfB4djQWaLBtT-hGiG9tonWjBzuX44W4f5oW37sV7rA22Vtc17WRXBc_MbOKxtr5iIHg03MLkBDXHL_chuBr6FiHuIloyAERtOQYSWdA\\\\\",\\\\\"eARKkW935slJb0V9pVqxsw:APA91bHmn-nhRCN2_rn7kvwnhq-rgCCh57uj6-wIqpDfw7IfqNo5YpH6CpApxC2u0Tuj4U37A6FPMsprikJHi7TuIw7j_SRkdi6Gu3XqizvETln3yQT4Xns\\\\\",\\\\\"fWAcK-S3yB63gUkueA5fnw:APA91bFkDV_11QCegZ3hPWl4Db0YxG8fjM5mGhSD2jkqXWPRzeL-IDuUwM2kC24Fzi0HifdIw4bRxDnaG_hc2TYHfaOsUOZrBtsbsnyFAOY92nNSjWaWwtQ\\\\\",\\\\\"cl6yJ-EEzJ4MIsfuGYkG1b:APA91bGK42Uumm4karP_-P6qRaEOt-vlYtBhmolc2oWwBAzoIOdP7nw0NNGBwxn45WPtCc3MLmrMALafYDuyN5KWftP0S38nP_gD5xJdxIJdWKh4wrD515I\\\\\",\\\\\"c3aNdhe20-3kp7p_owkF5K:APA91bFR57VUmUmh39dNlHKw5eJv6Ob6accOpnrWzFw8578MYIGMhDGsSAWTxuWt-DprWV5WquBSxgNp896AxmEHlbmzflA-ZgPBFWKr0rEBy9JddtR2OhY\\\\\",\\\\\"dTViIlKMjZED-A4q1MDKr5:APA91bHaYFLluDAT5ziCliwBHoBvy875cLsiH669iSPspQq_dRvBM8dVYjPyb0iDsX1Xv106RGy69cNM6j9gpeIMKVT2mePoRSsnqOvT_lfnSZjCA1AsS3A\\\\\",\\\\\"e3IYg5iKxK8nAbGgtiAvOa:APA91bH9F_R-vZugO7D2mtlNwoGEMfCDRS1-7wCuFNN17wOcPTh_8VCLc58esZUTWYZyLDL0aXWX8p7JKAy-M6Ba_NXtZBNAYfYlg1RFxRDjc-vwCSrhjAI\\\\\",\\\\\"crsP87t7skv02ykw__Tiry:APA91bHHKj1Y2Quqlnn28Xsc1runWQDqzDX0-gd0Vch8N6d0nvHuJsDwy-fVWD-Ds4KdIwdNJvKYrIE_jFcLMyYenpF4qI8kns1-7JFd9W4AkphuUJKRrsE\\\\\",\\\\\"fkfH7WboiHR2__iMV_eMhr:APA91bG85Zhto_Iw0sHBIiLnwnTRz2ev6dGg_f0ydk485yuyU3ds8x-PglqJ50jfKSX4q_wFhl8c1d8-v7Cx-_4ymmT5H-bf30GBBr4PoyXSf30OXyLGpH4\\\\\"]\\\"}', 'user_updated', '2025-07-21 20:15:21', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\User", "101", "{&quot;birthday&quot;:&quot;1991-08-10&quot;,&quot;firebase_token&quot;:&quot;[\\&quot;eScsKjJzGjO5cBX_P6BoD8:APA91bFgwIkbcVHE9uUo8a40dTrq02s4ajvEnHJXRsQRIzqiZ2QVz37PEt1d7t_-vgccvvLDV3S1Q5kD261ZiLn0syHQWNtq5glKT75eWtB5dTqBq1FCWqk\\&quot;,\\&quot;fG1TjZuLX1Fm4Krsby0Oa0:APA91bE2tLje2WBNhykCd99eRf9XLf5YXjg8Njg1gAy4Fj6z4yXiye7Ta5CU5MKWk455Ycpn1MzxM9onDf8Eqcb8J-TbvA6bKsKQRvznSIVBEbgWiC7gEE4\\&quot;,\\&quot;e3Xu5GMVPOsYzpe-yy6Odl:APA91bGOtv6swLQJENQDpt1BdRkFxzQT_OyT--f4m_z30Qs_QvMZ7yw_X-LaAOTkfj6CN3I3LSI4RSjN-7DhvD0LlYbKhGViZEQkjckxsj-lJIQUSB_LXK0\\&quot;,\\&quot;d3MaTWtjAmZYt-b47x5Ey0:APA91bEoKfumqFlNEAXBjPhTJdOBzddWz4DVPxZY_hfgFcmzYfaLhzhcHmYCdy_p39mpoUjadKB1G3Q7tD-QcyrrRvyBWAfG_qQiKX8VFNj8BuS6gkKcsT4\\&quot;,\\&quot;f5-22sIRSkSDuH27bmOYQk:APA91bFsQsL31RyWKiBlzr3NOI8quLbI-kH4rCTYEabc0KdqclttCLd9c4CspXm7d6NhD83V4qPw0Z577qRfjJ9yP9Wll2TiKsTlc4OCMmmAwqtjEg4_t_A\\&quot;,\\&quot;dmZqIMJeIa2SalIJB8izgV:APA91bFjqSNb3U86toipnosh291ovt-fLDlZucTBfgpPsgXdKRnNM4lekYNB7ir7-BbZbsbzS2_DaXv1dbqNPZ8z7TJ6nocLOVv7jS5Rogw50amMWIhy0EY\\&quot;,\\&quot;dAEmiMMREA7IIR_AEM8d4X:APA91bEvGV1nwVWxxs1Pyae0PXcPJyVkmesRWa5kzZh-XjFR3zmNRgu8KbekpiODL7hZcDVI7LuZhNejrfXu4bA3MuEBZFfJOl80daDASdNQA87av4M-TKk\\&quot;,\\&quot;eVfXF9-rpqO_skEsPW9Xrt:APA91bEp-Jm4Xr_LauNVQ0PDBdDpIb3YPXmcnEyJBYjWb78K0tDvFkDvJLgtc3ZUMVjojjUy2lPXJVvS4pjM08Eqpjt4LrByrzssBaE8B9aILeTLrC-oHrU\\&quot;,\\&quot;cY-v5gZveKVCTEmkABMyAP:APA91bHzovDtt5QfN1UX_72tOi-UZft1mdxKwd8UFE9dLRFoifZyjQQtK3_spM3hLX-PyzDR4VoJ80aaiJMIcsCIQCrWuh3kY2QVrWQcUxuvjTazVNR4uDo\\&quot;,\\&quot;dqk0tVQuQ9uRyYiU3FqkC4:APA91bEeCo5VX3mhfB4djQWaLBtT-hGiG9tonWjBzuX44W4f5oW37sV7rA22Vtc17WRXBc_MbOKxtr5iIHg03MLkBDXHL_chuBr6FiHuIloyAERtOQYSWdA\\&quot;,\\&quot;eARKkW935slJb0V9pVqxsw:APA91bHmn-nhRCN2_rn7kvwnhq-rgCCh57uj6-wIqpDfw7IfqNo5YpH6CpApxC2u0Tuj4U37A6FPMsprikJHi7TuIw7j_SRkdi6Gu3XqizvETln3yQT4Xns\\&quot;,\\&quot;fWAcK-S3yB63gUkueA5fnw:APA91bFkDV_11QCegZ3hPWl4Db0YxG8fjM5mGhSD2jkqXWPRzeL-IDuUwM2kC24Fzi0HifdIw4bRxDnaG_hc2TYHfaOsUOZrBtsbsnyFAOY92nNSjWaWwtQ\\&quot;,\\&quot;cl6yJ-EEzJ4MIsfuGYkG1b:APA91bGK42Uumm4karP_-P6qRaEOt-vlYtBhmolc2oWwBAzoIOdP7nw0NNGBwxn45WPtCc3MLmrMALafYDuyN5KWftP0S38nP_gD5xJdxIJdWKh4wrD515I\\&quot;,\\&quot;c3aNdhe20-3kp7p_owkF5K:APA91bFR57VUmUmh39dNlHKw5eJv6Ob6accOpnrWzFw8578MYIGMhDGsSAWTxuWt-DprWV5WquBSxgNp896AxmEHlbmzflA-ZgPBFWKr0rEBy9JddtR2OhY\\&quot;,\\&quot;dTViIlKMjZED-A4q1MDKr5:APA91bHaYFLluDAT5ziCliwBHoBvy875cLsiH669iSPspQq_dRvBM8dVYjPyb0iDsX1Xv106RGy69cNM6j9gpeIMKVT2mePoRSsnqOvT_lfnSZjCA1AsS3A\\&quot;,\\&quot;e3IYg5iKxK8nAbGgtiAvOa:APA91bH9F_R-vZugO7D2mtlNwoGEMfCDRS1-7wCuFNN17wOcPTh_8VCLc58esZUTWYZyLDL0aXWX8p7JKAy-M6Ba_NXtZBNAYfYlg1RFxRDjc-vwCSrhjAI\\&quot;,\\&quot;crsP87t7skv02ykw__Tiry:APA91bHHKj1Y2Quqlnn28Xsc1runWQDqzDX0-gd0Vch8N6d0nvHuJsDwy-fVWD-Ds4KdIwdNJvKYrIE_jFcLMyYenpF4qI8kns1-7JFd9W4AkphuUJKRrsE\\&quot;,\\&quot;fkfH7WboiHR2__iMV_eMhr:APA91bG85Zhto_Iw0sHBIiLnwnTRz2ev6dGg_f0ydk485yuyU3ds8x-PglqJ50jfKSX4q_wFhl8c1d8-v7Cx-_4ymmT5H-bf30GBBr4PoyXSf30OXyLGpH4\\&quot;]&quot;}", "user_updated", "2025-07-21 20:15:21", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\UserObserver.php", "line": 58}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 157}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0022299999999999998, "duration_str": "2.23ms", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 85.931, "width_percent": 14.069}]}, "models": {"data": {"App\\Models\\User": 2, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 3, "App\\Models\\Language": 3}, "count": 9}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f727708-2362-4a04-9202-2c84733758ef\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/user/profile/firebase/token/update", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-65701077 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-65701077\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1439252330 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_token</span>\" => \"<span class=sf-dump-str title=\"142 characters\">czShvYoPj2uhSRtRctMkvt:APA91bGzlAzreJ7qhrZdAzmdd5QYineJLoE7SriA7666wnYec5TAdHdYH7L0WaoxNJMbGi-RznCxs2aOwFPVXMnckSGVjoaRvoWxZpd13seCfEH8OPJAXOU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439252330\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-895587662 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">163</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 60|QteDihRKnlfKpNGKpZD1AbR1DpVeIMRNgMcSklKQ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895587662\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1687093169 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63320</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/api/v1/dashboard/user/profile/firebase/token/update</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/api/v1/dashboard/user/profile/firebase/token/update</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/index.php/api/v1/dashboard/user/profile/firebase/token/update</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">163</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">163</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 60|QteDihRKnlfKpNGKpZD1AbR1DpVeIMRNgMcSklKQ</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753139720.8105</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753139720</span>\n  \"<span class=sf-dump-key>argv</span>\" => []\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687093169\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2014405258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2014405258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-718486151 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 23:15:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4981</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718486151\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-139731618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-139731618\", {\"maxDepth\":0})</script>\n"}}