{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\seller-views\\\\recepts\\\\recept-ingredients.js\",\n  _s = $RefreshSig$();\nimport { Button, Col, Form, Row, Space } from 'antd';\nimport React from 'react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReceptIngredients = ({\n  next,\n  prev\n}) => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const form = Form.useFormInstance();\n  const {\n    defaultLang,\n    languages\n  } = useSelector(state => state.formLang, shallowEqual);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 12,\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: languages.map(item => /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: t('ingredients'),\n          name: ['ingredient', item.locale],\n          valuePropName: \"data\",\n          getValueFromEvent: (event, editor) => {\n            const data = editor.getData();\n            return data;\n          },\n          rules: [{\n            required: item.locale === defaultLang,\n            message: t('required')\n          }],\n          hidden: item.locale !== defaultLang,\n          children: /*#__PURE__*/_jsxDEV(CKEditor, {\n            editor: ClassicEditor,\n            data: form.getFieldValue(['ingredient', item.locale]) || '',\n            onReady: editor => {\n              // Editor is ready to use\n            },\n            onError: (error, {\n              willEditorRestart\n            }) => {\n              if (willEditorRestart) {\n                editor.ui.view.toolbar.element.style.display = 'none';\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 15\n          }, this)\n        }, `ingredient-${item.locale}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"button\",\n        onClick: () => prev(),\n        children: t('prev')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"button\",\n        onClick: () => {\n          form.validateFields([['ingredient', defaultLang]]).then(() => {\n            next();\n          });\n        },\n        children: t('next')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ReceptIngredients, \"kMugKneC68IWpwmWGhvC3N2jeHY=\", false, function () {\n  return [useTranslation, Form.useFormInstance, useSelector];\n});\n_c = ReceptIngredients;\nexport default ReceptIngredients;\nvar _c;\n$RefreshReg$(_c, \"ReceptIngredients\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Col", "Form", "Row", "Space", "React", "shallowEqual", "useSelector", "CKEditor", "ClassicEditor", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReceptIngredients", "next", "prev", "_s", "t", "form", "useFormInstance", "defaultLang", "languages", "state", "formLang", "children", "gutter", "span", "map", "item", "<PERSON><PERSON>", "label", "name", "locale", "valuePropName", "getValueFromEvent", "event", "editor", "data", "getData", "rules", "required", "message", "hidden", "getFieldValue", "onReady", "onError", "error", "willEditorRestart", "ui", "view", "toolbar", "element", "style", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "htmlType", "onClick", "validateFields", "then", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/seller-views/recepts/recept-ingredients.js"], "sourcesContent": ["import { Button, Col, Form, Row, Space } from 'antd';\nimport React from 'react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { useTranslation } from 'react-i18next';\n\nconst ReceptIngredients = ({ next, prev }) => {\n  const { t } = useTranslation();\n  const form = Form.useFormInstance();\n  const { defaultLang, languages } = useSelector(\n    (state) => state.formLang,\n    shallowEqual\n  );\n  return (\n    <>\n      <Row gutter={12}>\n        <Col span={24}>\n          {languages.map((item) => (\n            <Form.Item\n              key={`ingredient-${item.locale}`}\n              label={t('ingredients')}\n              name={['ingredient', item.locale]}\n              valuePropName='data'\n              getValueFromEvent={(event, editor) => {\n                const data = editor.getData();\n                return data;\n              }}\n              rules={[\n                {\n                  required: item.locale === defaultLang,\n                  message: t('required'),\n                },\n              ]}\n              hidden={item.locale !== defaultLang}\n            >\n              <CKEditor\n                editor={ClassicEditor}\n                data={form.getFieldValue(['ingredient', item.locale]) || ''}\n                onReady={(editor) => {\n                  // Editor is ready to use\n                }}\n                onError={(error, { willEditorRestart }) => {\n                  if (willEditorRestart) {\n                    editor.ui.view.toolbar.element.style.display = 'none';\n                  }\n                }}\n              />\n            </Form.Item>\n          ))}\n        </Col>\n      </Row>\n      <Space>\n        <Button type='primary' htmlType='button' onClick={() => prev()}>\n          {t('prev')}\n        </Button>\n        <Button\n          type='primary'\n          htmlType='button'\n          onClick={() => {\n            form.validateFields([['ingredient', defaultLang]]).then(() => {\n              next();\n            });\n          }}\n        >\n          {t('next')}\n        </Button>\n      </Space>\n    </>\n  );\n};\n\nexport default ReceptIngredients;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACvD,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM;IAAEC;EAAE,CAAC,GAAGT,cAAc,CAAC,CAAC;EAC9B,MAAMU,IAAI,GAAGlB,IAAI,CAACmB,eAAe,CAAC,CAAC;EACnC,MAAM;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGhB,WAAW,CAC3CiB,KAAK,IAAKA,KAAK,CAACC,QAAQ,EACzBnB,YACF,CAAC;EACD,oBACEM,OAAA,CAAAE,SAAA;IAAAY,QAAA,gBACEd,OAAA,CAACT,GAAG;MAACwB,MAAM,EAAE,EAAG;MAAAD,QAAA,eACdd,OAAA,CAACX,GAAG;QAAC2B,IAAI,EAAE,EAAG;QAAAF,QAAA,EACXH,SAAS,CAACM,GAAG,CAAEC,IAAI,iBAClBlB,OAAA,CAACV,IAAI,CAAC6B,IAAI;UAERC,KAAK,EAAEb,CAAC,CAAC,aAAa,CAAE;UACxBc,IAAI,EAAE,CAAC,YAAY,EAAEH,IAAI,CAACI,MAAM,CAAE;UAClCC,aAAa,EAAC,MAAM;UACpBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;YACpC,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAAC,CAAC;YAC7B,OAAOD,IAAI;UACb,CAAE;UACFE,KAAK,EAAE,CACL;YACEC,QAAQ,EAAEZ,IAAI,CAACI,MAAM,KAAKZ,WAAW;YACrCqB,OAAO,EAAExB,CAAC,CAAC,UAAU;UACvB,CAAC,CACD;UACFyB,MAAM,EAAEd,IAAI,CAACI,MAAM,KAAKZ,WAAY;UAAAI,QAAA,eAEpCd,OAAA,CAACJ,QAAQ;YACP8B,MAAM,EAAE7B,aAAc;YACtB8B,IAAI,EAAEnB,IAAI,CAACyB,aAAa,CAAC,CAAC,YAAY,EAAEf,IAAI,CAACI,MAAM,CAAC,CAAC,IAAI,EAAG;YAC5DY,OAAO,EAAGR,MAAM,IAAK;cACnB;YAAA,CACA;YACFS,OAAO,EAAEA,CAACC,KAAK,EAAE;cAAEC;YAAkB,CAAC,KAAK;cACzC,IAAIA,iBAAiB,EAAE;gBACrBX,MAAM,CAACY,EAAE,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;cACvD;YACF;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA3BI,cAAa7B,IAAI,CAACI,MAAO,EAAC;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4BvB,CACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN/C,OAAA,CAACR,KAAK;MAAAsB,QAAA,gBACJd,OAAA,CAACZ,MAAM;QAAC4D,IAAI,EAAC,SAAS;QAACC,QAAQ,EAAC,QAAQ;QAACC,OAAO,EAAEA,CAAA,KAAM7C,IAAI,CAAC,CAAE;QAAAS,QAAA,EAC5DP,CAAC,CAAC,MAAM;MAAC;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACT/C,OAAA,CAACZ,MAAM;QACL4D,IAAI,EAAC,SAAS;QACdC,QAAQ,EAAC,QAAQ;QACjBC,OAAO,EAAEA,CAAA,KAAM;UACb1C,IAAI,CAAC2C,cAAc,CAAC,CAAC,CAAC,YAAY,EAAEzC,WAAW,CAAC,CAAC,CAAC,CAAC0C,IAAI,CAAC,MAAM;YAC5DhD,IAAI,CAAC,CAAC;UACR,CAAC,CAAC;QACJ,CAAE;QAAAU,QAAA,EAEDP,CAAC,CAAC,MAAM;MAAC;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACzC,EAAA,CA/DIH,iBAAiB;EAAA,QACPL,cAAc,EACfR,IAAI,CAACmB,eAAe,EACEd,WAAW;AAAA;AAAA0D,EAAA,GAH1ClD,iBAAiB;AAiEvB,eAAeA,iBAAiB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}