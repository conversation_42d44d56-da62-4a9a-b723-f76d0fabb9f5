{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\app.js\",\n  _s = $RefreshSig$();\nimport Loading from 'components/loading';\nimport PageLoading from 'components/pageLoading';\nimport i18n from 'configs/i18next';\nimport { PathLogout } from 'context/path-logout';\nimport { ProtectedRoute } from 'context/protected-route';\nimport AppLayout from 'layout/app-layout';\nimport { WelcomeLayout } from 'layout/welcome-layout';\nimport Providers from 'providers';\nimport GoogleMapsProvider from 'components/GoogleMapsWrapper';\nimport { Suspense, useEffect, useState } from 'react';\nimport { Route, BrowserRouter as Router, Routes, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport { AllRoutes } from 'routes';\nimport informationService from 'services/rest/information';\nimport GlobalSettings from 'views/global-settings/global-settings';\nimport Login from 'views/login';\nimport NotFound from 'views/not-found';\nimport Welcome from 'views/welcome/welcome';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { cacheChecker } from './cacheChecker';\nimport { fetchRestSettings, fetchSettings } from './redux/slices/globalSettings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.auth, shallowEqual);\n  const [loading, setLoading] = useState(false);\n  const fetchUserSettings = role => {\n    switch (role) {\n      case 'admin':\n        dispatch(fetchSettings({}));\n        break;\n      case 'seller':\n        dispatch(fetchRestSettings({\n          seller: true\n        }));\n        break;\n      default:\n        dispatch(fetchRestSettings({}));\n    }\n  };\n  useEffect(() => {\n    cacheChecker();\n    return () => {};\n  }, []);\n  useEffect(() => {\n    fetchTranslations();\n    return () => {};\n  }, []);\n  useEffect(() => {\n    fetchUserSettings((user === null || user === void 0 ? void 0 : user.role) || '');\n    return () => {};\n  }, [user === null || user === void 0 ? void 0 : user.role]);\n  const fetchTranslations = () => {\n    const params = {\n      lang: i18n.language\n    };\n    setLoading(true);\n    informationService.translations(params).then(({\n      data\n    }) => i18n.addResourceBundle(i18n.language, 'translation', data)).finally(() => setLoading(false));\n  };\n  return /*#__PURE__*/_jsxDEV(Providers, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: [/*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          index: true,\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(PathLogout, {\n            children: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/welcome\",\n          element: /*#__PURE__*/_jsxDEV(WelcomeLayout, {\n            children: /*#__PURE__*/_jsxDEV(Welcome, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/installation\",\n          element: /*#__PURE__*/_jsxDEV(WelcomeLayout, {\n            children: /*#__PURE__*/_jsxDEV(GlobalSettings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AppLayout, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), AllRoutes.map(({\n            path,\n            component: Component\n          }) => /*#__PURE__*/_jsxDEV(Route, {\n            path: path,\n            element: /*#__PURE__*/_jsxDEV(Component, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 54\n            }, this)\n          }, path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Suspense, {\n            fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 35\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n        className: \"antd-toast\",\n        position: \"top-right\",\n        autoClose: 2500,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(PageLoading, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"R292UUcCvHgeghPPFyP9Wmi1DqA=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["Loading", "PageLoading", "i18n", "PathLogout", "ProtectedRoute", "AppLayout", "WelcomeLayout", "Providers", "GoogleMapsProvider", "Suspense", "useEffect", "useState", "Route", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Navigate", "ToastContainer", "AllRoutes", "informationService", "GlobalSettings", "<PERSON><PERSON>", "NotFound", "Welcome", "shallowEqual", "useDispatch", "useSelector", "cacheChecker", "fetchRestSettings", "fetchSettings", "jsxDEV", "_jsxDEV", "App", "_s", "dispatch", "user", "state", "auth", "loading", "setLoading", "fetchUserSettings", "role", "seller", "fetchTranslations", "params", "lang", "language", "translations", "then", "data", "addResourceBundle", "finally", "children", "index", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "map", "component", "Component", "fallback", "className", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/app.js"], "sourcesContent": ["import Loading from 'components/loading';\nimport PageLoading from 'components/pageLoading';\nimport i18n from 'configs/i18next';\nimport { PathLogout } from 'context/path-logout';\nimport { ProtectedRoute } from 'context/protected-route';\nimport AppLayout from 'layout/app-layout';\nimport { WelcomeLayout } from 'layout/welcome-layout';\nimport Providers from 'providers';\nimport GoogleMapsProvider from 'components/GoogleMapsWrapper';\nimport { Suspense, useEffect, useState } from 'react';\nimport {\n  Route,\n  BrowserRouter as Router,\n  Routes,\n  Navigate,\n} from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport { AllRoutes } from 'routes';\nimport informationService from 'services/rest/information';\nimport GlobalSettings from 'views/global-settings/global-settings';\nimport Login from 'views/login';\nimport NotFound from 'views/not-found';\nimport Welcome from 'views/welcome/welcome';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { cacheChecker } from './cacheChecker';\nimport {\n  fetchRestSettings,\n  fetchSettings,\n} from './redux/slices/globalSettings';\n\nconst App = () => {\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n\n  const [loading, setLoading] = useState(false);\n\n  const fetchUserSettings = (role) => {\n    switch (role) {\n      case 'admin':\n        dispatch(fetchSettings({}));\n        break;\n      case 'seller':\n        dispatch(fetchRestSettings({ seller: true }));\n        break;\n      default:\n        dispatch(fetchRestSettings({}));\n    }\n  };\n\n  useEffect(() => {\n    cacheChecker();\n    return () => {};\n  }, []);\n\n  useEffect(() => {\n    fetchTranslations();\n    return () => {};\n  }, []);\n\n  useEffect(() => {\n    fetchUserSettings(user?.role || '');\n    return () => {};\n  }, [user?.role]);\n\n  const fetchTranslations = () => {\n    const params = { lang: i18n.language };\n    setLoading(true);\n    informationService\n      .translations(params)\n      .then(({ data }) =>\n        i18n.addResourceBundle(i18n.language, 'translation', data),\n      )\n      .finally(() => setLoading(false));\n  };\n\n  return (\n    <Providers>\n      <Router>\n        <Routes>\n          <Route\n            index\n            path='/login'\n            element={\n              <PathLogout>\n                <Login />\n              </PathLogout>\n            }\n          />\n          <Route\n            path='/welcome'\n            element={\n              <WelcomeLayout>\n                <Welcome />\n              </WelcomeLayout>\n            }\n          />\n          <Route\n            path='/installation'\n            element={\n              <WelcomeLayout>\n                <GlobalSettings />\n              </WelcomeLayout>\n            }\n          />\n          <Route\n            path=''\n            element={\n              <ProtectedRoute>\n                <AppLayout />\n              </ProtectedRoute>\n            }\n          >\n            <Route path='/' element={<Navigate to='dashboard' />} />\n            {AllRoutes.map(({ path, component: Component }) => (\n              <Route key={path} path={path} element={<Component />} />\n            ))}\n          </Route>\n          <Route\n            path='*'\n            element={\n              <Suspense fallback={<Loading />}>\n                <NotFound />\n              </Suspense>\n            }\n          />\n        </Routes>\n        <ToastContainer\n          className='antd-toast'\n          position='top-right'\n          autoClose={2500}\n          hideProgressBar\n          closeOnClick\n          pauseOnHover\n          draggable\n        />\n        {loading && <PageLoading />}\n      </Router>\n    </Providers>\n  );\n};\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACrD,SACEC,KAAK,EACLC,aAAa,IAAIC,MAAM,EACvBC,MAAM,EACNC,QAAQ,QACH,kBAAkB;AACzB,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,SAAS,QAAQ,QAAQ;AAClC,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SACEC,iBAAiB,EACjBC,aAAa,QACR,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAK,CAAC,GAAGT,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEb,YAAY,CAAC;EAEjE,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM6B,iBAAiB,GAAIC,IAAI,IAAK;IAClC,QAAQA,IAAI;MACV,KAAK,OAAO;QACVP,QAAQ,CAACL,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF,KAAK,QAAQ;QACXK,QAAQ,CAACN,iBAAiB,CAAC;UAAEc,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC;QAC7C;MACF;QACER,QAAQ,CAACN,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACdiB,YAAY,CAAC,CAAC;IACd,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAENjB,SAAS,CAAC,MAAM;IACdiC,iBAAiB,CAAC,CAAC;IACnB,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAENjC,SAAS,CAAC,MAAM;IACd8B,iBAAiB,CAAC,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,KAAI,EAAE,CAAC;IACnC,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,CAACN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,CAAC,CAAC;EAEhB,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,MAAM,GAAG;MAAEC,IAAI,EAAE3C,IAAI,CAAC4C;IAAS,CAAC;IACtCP,UAAU,CAAC,IAAI,CAAC;IAChBpB,kBAAkB,CACf4B,YAAY,CAACH,MAAM,CAAC,CACpBI,IAAI,CAAC,CAAC;MAAEC;IAAK,CAAC,KACb/C,IAAI,CAACgD,iBAAiB,CAAChD,IAAI,CAAC4C,QAAQ,EAAE,aAAa,EAAEG,IAAI,CAC3D,CAAC,CACAE,OAAO,CAAC,MAAMZ,UAAU,CAAC,KAAK,CAAC,CAAC;EACrC,CAAC;EAED,oBACER,OAAA,CAACxB,SAAS;IAAA6C,QAAA,eACRrB,OAAA,CAACjB,MAAM;MAAAsC,QAAA,gBACLrB,OAAA,CAAChB,MAAM;QAAAqC,QAAA,gBACLrB,OAAA,CAACnB,KAAK;UACJyC,KAAK;UACLC,IAAI,EAAC,QAAQ;UACbC,OAAO,eACLxB,OAAA,CAAC5B,UAAU;YAAAiD,QAAA,eACTrB,OAAA,CAACV,KAAK;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF5B,OAAA,CAACnB,KAAK;UACJ0C,IAAI,EAAC,UAAU;UACfC,OAAO,eACLxB,OAAA,CAACzB,aAAa;YAAA8C,QAAA,eACZrB,OAAA,CAACR,OAAO;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAChB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF5B,OAAA,CAACnB,KAAK;UACJ0C,IAAI,EAAC,eAAe;UACpBC,OAAO,eACLxB,OAAA,CAACzB,aAAa;YAAA8C,QAAA,eACZrB,OAAA,CAACX,cAAc;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAChB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF5B,OAAA,CAACnB,KAAK;UACJ0C,IAAI,EAAC,EAAE;UACPC,OAAO,eACLxB,OAAA,CAAC3B,cAAc;YAAAgD,QAAA,eACbrB,OAAA,CAAC1B,SAAS;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACjB;UAAAP,QAAA,gBAEDrB,OAAA,CAACnB,KAAK;YAAC0C,IAAI,EAAC,GAAG;YAACC,OAAO,eAAExB,OAAA,CAACf,QAAQ;cAAC4C,EAAE,EAAC;YAAW;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvDzC,SAAS,CAAC2C,GAAG,CAAC,CAAC;YAAEP,IAAI;YAAEQ,SAAS,EAAEC;UAAU,CAAC,kBAC5ChC,OAAA,CAACnB,KAAK;YAAY0C,IAAI,EAAEA,IAAK;YAACC,OAAO,eAAExB,OAAA,CAACgC,SAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE,GAAzCL,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAuC,CACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACR5B,OAAA,CAACnB,KAAK;UACJ0C,IAAI,EAAC,GAAG;UACRC,OAAO,eACLxB,OAAA,CAACtB,QAAQ;YAACuD,QAAQ,eAAEjC,OAAA,CAAC/B,OAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAP,QAAA,eAC9BrB,OAAA,CAACT,QAAQ;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACX;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACT5B,OAAA,CAACd,cAAc;QACbgD,SAAS,EAAC,YAAY;QACtBC,QAAQ,EAAC,WAAW;QACpBC,SAAS,EAAE,IAAK;QAChBC,eAAe;QACfC,YAAY;QACZC,YAAY;QACZC,SAAS;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACDrB,OAAO,iBAAIP,OAAA,CAAC9B,WAAW;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAAC1B,EAAA,CA7GID,GAAG;EAAA,QACUP,WAAW,EACXC,WAAW;AAAA;AAAA8C,EAAA,GAFxBxC,GAAG;AA8GT,eAAeA,GAAG;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}