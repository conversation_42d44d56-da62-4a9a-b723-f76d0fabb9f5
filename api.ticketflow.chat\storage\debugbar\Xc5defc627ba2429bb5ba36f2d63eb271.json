{"__meta": {"id": "Xc5defc627ba2429bb5ba36f2d63eb271", "datetime": "2025-07-21 19:35:17", "utime": 1753137317.191434, "method": "POST", "uri": "/api/v1/auth/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[19:35:17] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753137317.043608, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753137316.793877, "end": 1753137317.191452, "duration": 0.39757513999938965, "duration_str": "398ms", "measures": [{"label": "Booting", "start": 1753137316.793877, "relative_start": 0, "end": 1753137317.023542, "relative_end": 1753137317.023542, "duration": 0.22966504096984863, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753137317.023562, "relative_start": 0.22968506813049316, "end": 1753137317.191457, "relative_end": 5.0067901611328125e-06, "duration": 0.16789507865905762, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 42012424, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/auth/login", "middleware": "api, block.ip, sessions", "controller": "App\\Http\\Controllers\\API\\v1\\Auth\\LoginController@login", "namespace": null, "prefix": "api/v1", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php&line=33\">\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php:33-53</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02516, "accumulated_duration_str": "25.16ms", "statements": [{"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.02077, "duration_str": "20.77ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 0, "width_percent": 82.552}, {"sql": "select * from `users` where `email` = '<EMAIL>' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 133}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 377}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php", "line": 39}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:133", "connection": "foodyman", "start_percent": 82.552, "width_percent": 2.464}, {"sql": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('api_token', 'db17fe0d590180b521b263b69163602fa7958b4ffd9053bc1409b55d8723ad6c', '[\\\"*\\\"]', 103, 'App\\Models\\User', '2025-07-21 19:35:17', '2025-07-21 19:35:17')", "type": "query", "params": [], "bindings": ["api_token", "db17fe0d590180b521b263b69163602fa7958b4ffd9053bc1409b55d8723ad6c", "[&quot;*&quot;]", "103", "App\\Models\\User", "2025-07-21 19:35:17", "2025-07-21 19:35:17"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php", "line": 46}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00181, "duration_str": "1.81ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php:46", "connection": "foodyman", "start_percent": 85.016, "width_percent": 7.194}, {"sql": "select * from `shops` where `shops`.`user_id` in (103) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php", "line": 51}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php:51", "connection": "foodyman", "start_percent": 92.21, "width_percent": 2.464}, {"sql": "select * from `request_models` where `request_models`.`model_id` in (103) and `request_models`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php", "line": 51}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Auth\\LoginController.php:51", "connection": "foodyman", "start_percent": 94.674, "width_percent": 3.259}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 103 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["103", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 212}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\UserResource.php", "line": 46}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Models\\User.php:212", "connection": "foodyman", "start_percent": 97.933, "width_percent": 2.067}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": 1, "App\\Models\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8cmOLuq67ELwioJeWdEkFswyN1yHkuOuQeCrg62A", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "103", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7268b4-14e9-423b-bc7d-afe2cf7b07c3\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/auth/login", "status_code": "<pre class=sf-dump id=sf-dump-378615709 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-378615709\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1591936706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1591936706\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-508402953 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">seller</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508402953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1643355548 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">52</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643355548\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-929830609 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57547</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/api/v1/auth/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/api/v1/auth/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"28 characters\">/index.php/api/v1/auth/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">52</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">52</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753137316.7939</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753137316</span>\n  \"<span class=sf-dump-key>argv</span>\" => []\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929830609\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1413813505 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1413813505\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1154504371 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 22:35:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4993</span>\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"142 characters\">foodyman_session=k7HCeUzCcJ4Ve6la186JeVwBPZkDtfWUAx9VRhOi; expires=Tue, 22-Jul-2025 00:35:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">foodyman_session=k7HCeUzCcJ4Ve6la186JeVwBPZkDtfWUAx9VRhOi; expires=Tue, 22-Jul-2025 00:35:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154504371\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1243412482 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8cmOLuq67ELwioJeWdEkFswyN1yHkuOuQeCrg62A</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>103</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243412482\", {\"maxDepth\":0})</script>\n"}}