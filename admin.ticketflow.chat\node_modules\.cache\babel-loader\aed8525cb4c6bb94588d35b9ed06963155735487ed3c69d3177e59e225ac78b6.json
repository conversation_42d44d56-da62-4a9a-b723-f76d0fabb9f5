{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\seller-views\\\\recepts\\\\recept-stocks.js\",\n  _s = $RefreshSig$();\nimport { DeleteOutlined, PlusOutlined } from '@ant-design/icons';\nimport { Button, Col, Form, InputNumber, Row, Space } from 'antd';\nimport React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { AsyncSelect } from '../../../components/async-select';\nimport productService from '../../../services/seller/product';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReceptStocks = ({\n  next,\n  prev\n}) => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const form = Form.useFormInstance();\n  const stocks = Form.useWatch('stocks', form);\n  function fetchProductsStock(search) {\n    const params = {\n      perPage: 10,\n      page: 1,\n      search,\n      active: 1\n    };\n    return productService.getStock(params).then(res => res.data.filter(stock => !stocks.map(item => {\n      var _item$stock_id;\n      return item === null || item === void 0 ? void 0 : (_item$stock_id = item.stock_id) === null || _item$stock_id === void 0 ? void 0 : _item$stock_id.value;\n    }).includes(stock.id)).map(stock => ({\n      label: stock.product.translation.title + ' ' + stock.extras.map(ext => ext.value).join(', '),\n      value: stock.id\n    })));\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 12,\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.List, {\n          name: \"stocks\",\n          initialValue: [{\n            stock_id: undefined,\n            min_quantity: undefined\n          }],\n          children: (fields, {\n            add,\n            remove\n          }) => /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [fields.map(({\n              key,\n              name,\n              ...restField\n            }, i) => /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 12,\n              align: \"middle\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 11,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  ...restField,\n                  label: t('stock'),\n                  name: [name, 'stock_id'],\n                  rules: [{\n                    required: true,\n                    message: t('required')\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(AsyncSelect, {\n                    fetchOptions: fetchProductsStock,\n                    debounceTimeout: 200\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 11,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  ...restField,\n                  label: t('min.quantity'),\n                  name: [name, 'min_quantity'],\n                  rules: [{\n                    required: true,\n                    message: t('required')\n                  }, {\n                    type: 'number',\n                    min: 1,\n                    message: t('must.be.at.least.1')\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    className: \"w-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 21\n              }, this), i !== 0 && /*#__PURE__*/_jsxDEV(Col, {\n                span: 2,\n                className: \"d-flex justify-content-end\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => remove(name),\n                  danger: true,\n                  className: \"w-100\",\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 33\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 23\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => add(),\n                block: true,\n                icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 61\n                }, this),\n                children: t('add.stock')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"button\",\n        onClick: () => prev(),\n        children: t('prev')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"button\",\n        onClick: () => {\n          form.validateFields(stocks.flatMap((stock, i) => [['stocks', i, 'stock_id'], ['stocks', i, 'min_quantity']])).then(() => {\n            next();\n          });\n        },\n        children: t('next')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ReceptStocks, \"Wxyq2rOUgotQTmXcav1hdPFpz/w=\", false, function () {\n  return [useTranslation, Form.useFormInstance, Form.useWatch];\n});\n_c = ReceptStocks;\nexport default ReceptStocks;\nvar _c;\n$RefreshReg$(_c, \"ReceptStocks\");", "map": {"version": 3, "names": ["DeleteOutlined", "PlusOutlined", "<PERSON><PERSON>", "Col", "Form", "InputNumber", "Row", "Space", "React", "useTranslation", "AsyncSelect", "productService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReceptStocks", "next", "prev", "_s", "t", "form", "useFormInstance", "stocks", "useWatch", "fetchProductsStock", "search", "params", "perPage", "page", "active", "getStock", "then", "res", "data", "filter", "stock", "map", "item", "_item$stock_id", "stock_id", "value", "includes", "id", "label", "product", "translation", "title", "extras", "ext", "join", "children", "gutter", "span", "List", "name", "initialValue", "undefined", "min_quantity", "fields", "add", "remove", "key", "restField", "i", "align", "<PERSON><PERSON>", "rules", "required", "message", "fetchOptions", "debounceTimeout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "min", "className", "onClick", "danger", "icon", "block", "htmlType", "validateFields", "flatMap", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/seller-views/recepts/recept-stocks.js"], "sourcesContent": ["import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';\nimport { Button, Col, Form, InputNumber, Row, Space } from 'antd';\nimport React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { AsyncSelect } from '../../../components/async-select';\nimport productService from '../../../services/seller/product';\n\nconst ReceptStocks = ({ next, prev }) => {\n  const { t } = useTranslation();\n  const form = Form.useFormInstance();\n  const stocks = Form.useWatch('stocks', form);\n  function fetchProductsStock(search) {\n    const params = {\n      perPage: 10,\n      page: 1,\n      search,\n      active: 1,\n    };\n    return productService.getStock(params).then((res) =>\n      res.data\n        .filter(\n          (stock) =>\n            !stocks.map((item) => item?.stock_id?.value).includes(stock.id)\n        )\n        .map((stock) => ({\n          label:\n            stock.product.translation.title +\n            ' ' +\n            stock.extras.map((ext) => ext.value).join(', '),\n          value: stock.id,\n        }))\n    );\n  }\n  return (\n    <>\n      <Row gutter={12}>\n        <Col span={24}>\n          <Form.List\n            name='stocks'\n            initialValue={[{ stock_id: undefined, min_quantity: undefined }]}\n          >\n            {(fields, { add, remove }) => (\n              <>\n                {fields.map(({ key, name, ...restField }, i) => (\n                  <Row key={key} gutter={12} align='middle'>\n                    <Col span={11}>\n                      <Form.Item\n                        {...restField}\n                        label={t('stock')}\n                        name={[name, 'stock_id']}\n                        rules={[\n                          {\n                            required: true,\n                            message: t('required'),\n                          },\n                        ]}\n                      >\n                        <AsyncSelect\n                          fetchOptions={fetchProductsStock}\n                          debounceTimeout={200}\n                        />\n                      </Form.Item>\n                    </Col>\n                    <Col span={11}>\n                      <Form.Item\n                        {...restField}\n                        label={t('min.quantity')}\n                        name={[name, 'min_quantity']}\n                        rules={[\n                          {\n                            required: true,\n                            message: t('required'),\n                          },\n                          {\n                            type: 'number',\n                            min: 1,\n                            message: t('must.be.at.least.1'),\n                          },\n                        ]}\n                      >\n                        <InputNumber className='w-100' />\n                      </Form.Item>\n                    </Col>\n                    {i !== 0 && (\n                      <Col span={2} className='d-flex justify-content-end'>\n                        <Button\n                          onClick={() => remove(name)}\n                          danger\n                          className='w-100'\n                          type='primary'\n                          icon={<DeleteOutlined />}\n                        />\n                      </Col>\n                    )}\n                  </Row>\n                ))}\n\n                <Form.Item>\n                  <Button onClick={() => add()} block icon={<PlusOutlined />}>\n                    {t('add.stock')}\n                  </Button>\n                </Form.Item>\n              </>\n            )}\n          </Form.List>\n        </Col>\n      </Row>\n      <Space>\n        <Button type='primary' htmlType='button' onClick={() => prev()}>\n          {t('prev')}\n        </Button>\n        <Button\n          type='primary'\n          htmlType='button'\n          onClick={() => {\n            form\n              .validateFields(\n                stocks.flatMap((stock, i) => [\n                  ['stocks', i, 'stock_id'],\n                  ['stocks', i, 'min_quantity'],\n                ])\n              )\n              .then(() => {\n                next();\n              });\n          }}\n        >\n          {t('next')}\n        </Button>\n      </Space>\n    </>\n  );\n};\n\nexport default ReceptStocks;\n"], "mappings": ";;AAAA,SAASA,cAAc,EAAEC,YAAY,QAAQ,mBAAmB;AAChE,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AACjE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,cAAc,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,YAAY,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAE,CAAC,GAAGX,cAAc,CAAC,CAAC;EAC9B,MAAMY,IAAI,GAAGjB,IAAI,CAACkB,eAAe,CAAC,CAAC;EACnC,MAAMC,MAAM,GAAGnB,IAAI,CAACoB,QAAQ,CAAC,QAAQ,EAAEH,IAAI,CAAC;EAC5C,SAASI,kBAAkBA,CAACC,MAAM,EAAE;IAClC,MAAMC,MAAM,GAAG;MACbC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,CAAC;MACPH,MAAM;MACNI,MAAM,EAAE;IACV,CAAC;IACD,OAAOnB,cAAc,CAACoB,QAAQ,CAACJ,MAAM,CAAC,CAACK,IAAI,CAAEC,GAAG,IAC9CA,GAAG,CAACC,IAAI,CACLC,MAAM,CACJC,KAAK,IACJ,CAACb,MAAM,CAACc,GAAG,CAAEC,IAAI;MAAA,IAAAC,cAAA;MAAA,OAAKD,IAAI,aAAJA,IAAI,wBAAAC,cAAA,GAAJD,IAAI,CAAEE,QAAQ,cAAAD,cAAA,uBAAdA,cAAA,CAAgBE,KAAK;IAAA,EAAC,CAACC,QAAQ,CAACN,KAAK,CAACO,EAAE,CAClE,CAAC,CACAN,GAAG,CAAED,KAAK,KAAM;MACfQ,KAAK,EACHR,KAAK,CAACS,OAAO,CAACC,WAAW,CAACC,KAAK,GAC/B,GAAG,GACHX,KAAK,CAACY,MAAM,CAACX,GAAG,CAAEY,GAAG,IAAKA,GAAG,CAACR,KAAK,CAAC,CAACS,IAAI,CAAC,IAAI,CAAC;MACjDT,KAAK,EAAEL,KAAK,CAACO;IACf,CAAC,CAAC,CACN,CAAC;EACH;EACA,oBACE9B,OAAA,CAAAE,SAAA;IAAAoC,QAAA,gBACEtC,OAAA,CAACP,GAAG;MAAC8C,MAAM,EAAE,EAAG;MAAAD,QAAA,eACdtC,OAAA,CAACV,GAAG;QAACkD,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZtC,OAAA,CAACT,IAAI,CAACkD,IAAI;UACRC,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE,CAAC;YAAEhB,QAAQ,EAAEiB,SAAS;YAAEC,YAAY,EAAED;UAAU,CAAC,CAAE;UAAAN,QAAA,EAEhEA,CAACQ,MAAM,EAAE;YAAEC,GAAG;YAAEC;UAAO,CAAC,kBACvBhD,OAAA,CAAAE,SAAA;YAAAoC,QAAA,GACGQ,MAAM,CAACtB,GAAG,CAAC,CAAC;cAAEyB,GAAG;cAAEP,IAAI;cAAE,GAAGQ;YAAU,CAAC,EAAEC,CAAC,kBACzCnD,OAAA,CAACP,GAAG;cAAW8C,MAAM,EAAE,EAAG;cAACa,KAAK,EAAC,QAAQ;cAAAd,QAAA,gBACvCtC,OAAA,CAACV,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAF,QAAA,eACZtC,OAAA,CAACT,IAAI,CAAC8D,IAAI;kBAAA,GACJH,SAAS;kBACbnB,KAAK,EAAExB,CAAC,CAAC,OAAO,CAAE;kBAClBmC,IAAI,EAAE,CAACA,IAAI,EAAE,UAAU,CAAE;kBACzBY,KAAK,EAAE,CACL;oBACEC,QAAQ,EAAE,IAAI;oBACdC,OAAO,EAAEjD,CAAC,CAAC,UAAU;kBACvB,CAAC,CACD;kBAAA+B,QAAA,eAEFtC,OAAA,CAACH,WAAW;oBACV4D,YAAY,EAAE7C,kBAAmB;oBACjC8C,eAAe,EAAE;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN9D,OAAA,CAACV,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAF,QAAA,eACZtC,OAAA,CAACT,IAAI,CAAC8D,IAAI;kBAAA,GACJH,SAAS;kBACbnB,KAAK,EAAExB,CAAC,CAAC,cAAc,CAAE;kBACzBmC,IAAI,EAAE,CAACA,IAAI,EAAE,cAAc,CAAE;kBAC7BY,KAAK,EAAE,CACL;oBACEC,QAAQ,EAAE,IAAI;oBACdC,OAAO,EAAEjD,CAAC,CAAC,UAAU;kBACvB,CAAC,EACD;oBACEwD,IAAI,EAAE,QAAQ;oBACdC,GAAG,EAAE,CAAC;oBACNR,OAAO,EAAEjD,CAAC,CAAC,oBAAoB;kBACjC,CAAC,CACD;kBAAA+B,QAAA,eAEFtC,OAAA,CAACR,WAAW;oBAACyE,SAAS,EAAC;kBAAO;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACLX,CAAC,KAAK,CAAC,iBACNnD,OAAA,CAACV,GAAG;gBAACkD,IAAI,EAAE,CAAE;gBAACyB,SAAS,EAAC,4BAA4B;gBAAA3B,QAAA,eAClDtC,OAAA,CAACX,MAAM;kBACL6E,OAAO,EAAEA,CAAA,KAAMlB,MAAM,CAACN,IAAI,CAAE;kBAC5ByB,MAAM;kBACNF,SAAS,EAAC,OAAO;kBACjBF,IAAI,EAAC,SAAS;kBACdK,IAAI,eAAEpE,OAAA,CAACb,cAAc;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA,GAjDOb,GAAG;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkDR,CACN,CAAC,eAEF9D,OAAA,CAACT,IAAI,CAAC8D,IAAI;cAAAf,QAAA,eACRtC,OAAA,CAACX,MAAM;gBAAC6E,OAAO,EAAEA,CAAA,KAAMnB,GAAG,CAAC,CAAE;gBAACsB,KAAK;gBAACD,IAAI,eAAEpE,OAAA,CAACZ,YAAY;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAxB,QAAA,EACxD/B,CAAC,CAAC,WAAW;cAAC;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,eACZ;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN9D,OAAA,CAACN,KAAK;MAAA4C,QAAA,gBACJtC,OAAA,CAACX,MAAM;QAAC0E,IAAI,EAAC,SAAS;QAACO,QAAQ,EAAC,QAAQ;QAACJ,OAAO,EAAEA,CAAA,KAAM7D,IAAI,CAAC,CAAE;QAAAiC,QAAA,EAC5D/B,CAAC,CAAC,MAAM;MAAC;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACT9D,OAAA,CAACX,MAAM;QACL0E,IAAI,EAAC,SAAS;QACdO,QAAQ,EAAC,QAAQ;QACjBJ,OAAO,EAAEA,CAAA,KAAM;UACb1D,IAAI,CACD+D,cAAc,CACb7D,MAAM,CAAC8D,OAAO,CAAC,CAACjD,KAAK,EAAE4B,CAAC,KAAK,CAC3B,CAAC,QAAQ,EAAEA,CAAC,EAAE,UAAU,CAAC,EACzB,CAAC,QAAQ,EAAEA,CAAC,EAAE,cAAc,CAAC,CAC9B,CACH,CAAC,CACAhC,IAAI,CAAC,MAAM;YACVf,IAAI,CAAC,CAAC;UACR,CAAC,CAAC;QACN,CAAE;QAAAkC,QAAA,EAED/B,CAAC,CAAC,MAAM;MAAC;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACxD,EAAA,CA7HIH,YAAY;EAAA,QACFP,cAAc,EACfL,IAAI,CAACkB,eAAe,EAClBlB,IAAI,CAACoB,QAAQ;AAAA;AAAA8D,EAAA,GAHxBtE,YAAY;AA+HlB,eAAeA,YAAY;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}