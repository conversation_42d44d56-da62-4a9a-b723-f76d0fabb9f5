{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\translations\\\\index.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport { Button, Card, Form, Input, Select, Space, Table } from 'antd';\nimport translationService from 'services/translation';\nimport { toast } from 'react-toastify';\nimport { EditOutlined, PlusCircleOutlined } from '@ant-design/icons';\nimport TranslationCreateModal from './translationCreateModal';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport SearchInput from 'components/search-input';\nimport { CgImport } from 'react-icons/cg';\nimport { addMenu } from 'redux/slices/menu';\nimport { useNavigate } from 'react-router-dom';\nimport { export_url } from 'configs/app-global';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditableContext = /*#__PURE__*/React.createContext(null);\nconst EditableRow = ({\n  index,\n  ...props\n}) => {\n  _s();\n  const [form] = Form.useForm();\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    component: false,\n    children: /*#__PURE__*/_jsxDEV(EditableContext.Provider, {\n      value: form,\n      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(EditableRow, \"rI7DrJIrFu7YmlGWYiMFTzs8jF0=\", false, function () {\n  return [Form.useForm];\n});\n_c = EditableRow;\nconst EditableCell = ({\n  title,\n  editable,\n  children,\n  dataIndex,\n  record,\n  handleSave,\n  ...restProps\n}) => {\n  _s2();\n  const {\n    t\n  } = useTranslation();\n  const [editing, setEditing] = useState(false);\n  const inputRef = useRef(null);\n  const form = useContext(EditableContext);\n  useEffect(() => {\n    if (editing) {\n      inputRef.current.focus();\n    }\n  }, [editing]);\n  const toggleEdit = () => {\n    setEditing(!editing);\n    form.setFieldsValue({\n      [dataIndex]: record[dataIndex]\n    });\n  };\n  const save = async () => {\n    try {\n      const values = await form.validateFields();\n      toggleEdit();\n      handleSave({\n        ...record,\n        ...values,\n        dataIndex\n      });\n    } catch (errInfo) {\n      console.log('Save failed:', errInfo);\n    }\n  };\n  let childNode = children;\n  if (editable) {\n    childNode = editing ? /*#__PURE__*/_jsxDEV(Form.Item, {\n      style: {\n        margin: 0\n      },\n      name: dataIndex,\n      rules: [{\n        validator(_, value) {\n          if (!value) {\n            return Promise.reject(new Error(t('required')));\n          } else if (value && (value === null || value === void 0 ? void 0 : value.trim()) === '') {\n            return Promise.reject(new Error(t('no.empty.space')));\n          } else if (value && (value === null || value === void 0 ? void 0 : value.trim().length) < 2) {\n            return Promise.reject(new Error(t('must.be.at.least.2')));\n          }\n          return Promise.resolve();\n        }\n      }],\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        ref: inputRef,\n        onPressEnter: save,\n        onBlur: save\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editable-cell-value-wrap cursor-pointer d-flex justify-content-between align-items-center\",\n      style: {\n        paddingRight: 24\n      },\n      onClick: toggleEdit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-100\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"td\", {\n    ...restProps,\n    children: childNode\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 10\n  }, this);\n};\n_s2(EditableCell, \"qVEDyH8JNd1woNPbkabL2AcfoGo=\", false, function () {\n  return [useTranslation];\n});\n_c2 = EditableCell;\nexport default function Translations() {\n  _s3();\n  const {\n    t\n  } = useTranslation();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [list, setList] = useState([]);\n  const [pageSize, setPageSize] = useState(10);\n  const [page, setPage] = useState(1);\n  const [total, setTotal] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [group, setGroup] = useState(null);\n  const [sort, setSort] = useState(null);\n  const [column, setColumn] = useState(null);\n  const [visible, setVisible] = useState(false);\n  const [skipPage, setSkipPage] = useState(0);\n  const {\n    languages\n  } = useSelector(state => state.formLang, shallowEqual);\n  const [locale, setLocale] = useState('');\n  const [search, setSearch] = useState('');\n  const [downloading, setDownloading] = useState(false);\n  const defaultColumns = useMemo(() => [{\n    title: t('name'),\n    dataIndex: 'key',\n    sorter: (a, b, sortOrder) => sortTable(sortOrder, 'key'),\n    width: 250,\n    fixed: 'left'\n  }, {\n    title: t('group'),\n    dataIndex: 'group',\n    sorter: (a, b, sortOrder) => sortTable(sortOrder, 'group'),\n    width: 150,\n    fixed: 'left'\n  }, ...languages.filter(item => locale ? item.locale === locale : true).map(item => ({\n    key: item.locale,\n    title: item.title,\n    dataIndex: `value[${item.locale}]`,\n    editable: true,\n    width: 300\n  }))], [languages, locale]);\n  function sortTable(type, column) {\n    let sortType;\n    switch (type) {\n      case 'ascend':\n        sortType = 'asc';\n        break;\n      case 'descend':\n        sortType = 'desc';\n        break;\n      default:\n        break;\n    }\n    setSort(sortType);\n    setColumn(column);\n  }\n  function fetchTranslations() {\n    setLoading(true);\n    const params = {\n      perPage: pageSize,\n      skip: skipPage,\n      group,\n      sort,\n      column,\n      search\n    };\n    translationService.getAll(params).then(({\n      data\n    }) => {\n      const translations = Object.entries(data.translations).map(item => ({\n        key: item[0],\n        group: item[1][0].group,\n        ...Object.assign({}, ...languages.map(lang => {\n          var _item$1$find;\n          return {\n            [`value[${lang.locale}]`]: (_item$1$find = item[1].find(el => el.locale === lang.locale)) === null || _item$1$find === void 0 ? void 0 : _item$1$find.value\n          };\n        }))\n      }));\n      setList(translations);\n      setTotal(data.total);\n    }).finally(() => setLoading(false));\n  }\n  useEffect(() => {\n    fetchTranslations();\n    // eslint-disable-next-line\n  }, [pageSize, group, sort, column, skipPage, search]);\n  const onChangePagination = pageNumber => {\n    const {\n      pageSize,\n      current\n    } = pageNumber;\n    const skip = (current - 1) * pageSize;\n    setPageSize(pageSize);\n    setPage(current);\n    setSkipPage(skip);\n  };\n  const handleSave = row => {\n    const {\n      dataIndex,\n      key\n    } = row;\n    const newData = [...list];\n    const index = newData.findIndex(item => row.key === item.key);\n    const item = newData[index];\n    if (item[dataIndex] === row[dataIndex]) {\n      return;\n    }\n    newData.splice(index, 1, {\n      ...item,\n      ...row\n    });\n    setList(newData);\n    const savedItem = {\n      ...row,\n      value: undefined,\n      dataIndex: undefined,\n      key: undefined\n    };\n    updateTranslation(key, savedItem);\n  };\n  function updateTranslation(key, data) {\n    translationService.update(key, data).then(res => toast.success(res.message));\n  }\n  const components = {\n    body: {\n      row: EditableRow,\n      cell: EditableCell\n    }\n  };\n  const columns = defaultColumns.map(col => {\n    if (!col.editable) {\n      return col;\n    }\n    return {\n      ...col,\n      onCell: record => ({\n        record,\n        editable: col.editable,\n        dataIndex: col.dataIndex,\n        title: col.title,\n        fixed: col.fixed,\n        handleSave\n      })\n    };\n  });\n  const excelExport = () => {\n    setDownloading(true);\n    translationService.export().then(res => {\n      window.location.href = export_url + res.data.file_name;\n    }).finally(() => setDownloading(false));\n  };\n  const goToImport = () => {\n    dispatch(addMenu({\n      id: 'translation-import',\n      url: `settings/translations/import`,\n      name: t('translation.import')\n    }));\n    navigate(`import`);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    extra: /*#__PURE__*/_jsxDEV(Space, {\n      wrap: true,\n      children: [/*#__PURE__*/_jsxDEV(SearchInput, {\n        placeholder: t('search'),\n        handleChange: search => setSearch(search)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        style: {\n          minWidth: 150\n        },\n        value: locale,\n        onChange: value => setLocale(value),\n        placeholder: t('select.language'),\n        children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n          value: \"\",\n          children: t('all')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this), languages.map(item => /*#__PURE__*/_jsxDEV(Select.Option, {\n          value: item.locale,\n          children: item.title\n        }, item.locale, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        style: {\n          minWidth: 150\n        },\n        value: group,\n        onChange: value => setGroup(value),\n        placeholder: t('select.group'),\n        children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n          value: \"\",\n          children: t('all')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n          value: \"web\",\n          children: t('web')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n          value: \"mobile\",\n          children: t('mobile')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n          value: \"errors\",\n          children: t('errors')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: excelExport,\n        loading: downloading,\n        children: [/*#__PURE__*/_jsxDEV(CgImport, {\n          className: \"mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), t('export')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: goToImport,\n        children: [/*#__PURE__*/_jsxDEV(CgImport, {\n          className: \"mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), t('import')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(PlusCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 19\n        }, this),\n        type: \"primary\",\n        onClick: () => setVisible(true),\n        children: t('add.translation')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 9\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(Table, {\n      components: components,\n      columns: columns,\n      dataSource: list,\n      pagination: {\n        pageSize,\n        page,\n        total\n      },\n      rowKey: record => record.key,\n      onChange: onChangePagination,\n      loading: loading,\n      scroll: {\n        x: 1500\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), visible && /*#__PURE__*/_jsxDEV(TranslationCreateModal, {\n      visible: visible,\n      setVisible: setVisible,\n      languages: languages,\n      refetch: fetchTranslations\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 288,\n    columnNumber: 5\n  }, this);\n}\n_s3(Translations, \"9IzTxro6H/oW4MMdeFdmlslDaSc=\", false, function () {\n  return [useTranslation, useDispatch, useNavigate, useSelector];\n});\n_c3 = Translations;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"EditableRow\");\n$RefreshReg$(_c2, \"EditableCell\");\n$RefreshReg$(_c3, \"Translations\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useMemo", "useRef", "useState", "<PERSON><PERSON>", "Card", "Form", "Input", "Select", "Space", "Table", "translationService", "toast", "EditOutlined", "PlusCircleOutlined", "TranslationCreateModal", "shallowEqual", "useDispatch", "useSelector", "useTranslation", "SearchInput", "CgImport", "addMenu", "useNavigate", "export_url", "jsxDEV", "_jsxDEV", "EditableContext", "createContext", "EditableRow", "index", "props", "_s", "form", "useForm", "component", "children", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "EditableCell", "title", "editable", "dataIndex", "record", "handleSave", "restProps", "_s2", "t", "editing", "setEditing", "inputRef", "current", "focus", "toggleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save", "values", "validateFields", "errInfo", "console", "log", "childNode", "<PERSON><PERSON>", "style", "margin", "name", "rules", "validator", "_", "Promise", "reject", "Error", "trim", "length", "resolve", "ref", "onPressEnter", "onBlur", "className", "paddingRight", "onClick", "_c2", "Translations", "_s3", "dispatch", "navigate", "list", "setList", "pageSize", "setPageSize", "page", "setPage", "total", "setTotal", "loading", "setLoading", "group", "setGroup", "sort", "setSort", "column", "setColumn", "visible", "setVisible", "skipPage", "setSkipPage", "languages", "state", "formLang", "locale", "setLocale", "search", "setSearch", "downloading", "setDownloading", "defaultColumns", "sorter", "a", "b", "sortOrder", "sortTable", "width", "fixed", "filter", "item", "map", "key", "type", "sortType", "fetchTranslations", "params", "perPage", "skip", "getAll", "then", "data", "translations", "Object", "entries", "assign", "lang", "_item$1$find", "find", "el", "finally", "onChangePagination", "pageNumber", "row", "newData", "findIndex", "splice", "savedItem", "undefined", "updateTranslation", "update", "res", "success", "message", "components", "body", "cell", "columns", "col", "onCell", "excelExport", "export", "window", "location", "href", "file_name", "goToImport", "id", "url", "extra", "wrap", "placeholder", "handleChange", "min<PERSON><PERSON><PERSON>", "onChange", "Option", "icon", "dataSource", "pagination", "<PERSON><PERSON><PERSON>", "scroll", "x", "refetch", "_c3", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/translations/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport { Button, Card, Form, Input, Select, Space, Table } from 'antd';\nimport translationService from 'services/translation';\nimport { toast } from 'react-toastify';\nimport { EditOutlined, PlusCircleOutlined } from '@ant-design/icons';\nimport TranslationCreateModal from './translationCreateModal';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport SearchInput from 'components/search-input';\nimport { CgImport } from 'react-icons/cg';\nimport { addMenu } from 'redux/slices/menu';\nimport { useNavigate } from 'react-router-dom';\nimport { export_url } from 'configs/app-global';\n\nconst EditableContext = React.createContext(null);\n\nconst EditableRow = ({ index, ...props }) => {\n  const [form] = Form.useForm();\n  return (\n    <Form form={form} component={false}>\n      <EditableContext.Provider value={form}>\n        <tr {...props} />\n      </EditableContext.Provider>\n    </Form>\n  );\n};\n\nconst EditableCell = ({\n  title,\n  editable,\n  children,\n  dataIndex,\n  record,\n  handleSave,\n  ...restProps\n}) => {\n  const { t } = useTranslation();\n  const [editing, setEditing] = useState(false);\n  const inputRef = useRef(null);\n  const form = useContext(EditableContext);\n  useEffect(() => {\n    if (editing) {\n      inputRef.current.focus();\n    }\n  }, [editing]);\n\n  const toggleEdit = () => {\n    setEditing(!editing);\n    form.setFieldsValue({\n      [dataIndex]: record[dataIndex],\n    });\n  };\n\n  const save = async () => {\n    try {\n      const values = await form.validateFields();\n      toggleEdit();\n      handleSave({ ...record, ...values, dataIndex });\n    } catch (errInfo) {\n      console.log('Save failed:', errInfo);\n    }\n  };\n\n  let childNode = children;\n\n  if (editable) {\n    childNode = editing ? (\n      <Form.Item\n        style={{\n          margin: 0,\n        }}\n        name={dataIndex}\n        rules={[\n          {\n            validator(_, value) {\n              if (!value) {\n                return Promise.reject(new Error(t('required')));\n              } else if (value && value?.trim() === '') {\n                return Promise.reject(new Error(t('no.empty.space')));\n              } else if (value && value?.trim().length < 2) {\n                return Promise.reject(new Error(t('must.be.at.least.2')));\n              }\n              return Promise.resolve();\n            },\n          },\n        ]}\n      >\n        <Input ref={inputRef} onPressEnter={save} onBlur={save} />\n      </Form.Item>\n    ) : (\n      <div\n        className='editable-cell-value-wrap cursor-pointer d-flex justify-content-between align-items-center'\n        style={{\n          paddingRight: 24,\n        }}\n        onClick={toggleEdit}\n      >\n        <div className='w-100'>{children}</div>\n        <EditOutlined />\n      </div>\n    );\n  }\n\n  return <td {...restProps}>{childNode}</td>;\n};\n\nexport default function Translations() {\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [list, setList] = useState([]);\n  const [pageSize, setPageSize] = useState(10);\n  const [page, setPage] = useState(1);\n  const [total, setTotal] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [group, setGroup] = useState(null);\n  const [sort, setSort] = useState(null);\n  const [column, setColumn] = useState(null);\n  const [visible, setVisible] = useState(false);\n  const [skipPage, setSkipPage] = useState(0);\n  const { languages } = useSelector((state) => state.formLang, shallowEqual);\n  const [locale, setLocale] = useState('');\n  const [search, setSearch] = useState('');\n  const [downloading, setDownloading] = useState(false);\n\n  const defaultColumns = useMemo(\n    () => [\n      {\n        title: t('name'),\n        dataIndex: 'key',\n        sorter: (a, b, sortOrder) => sortTable(sortOrder, 'key'),\n        width: 250,\n        fixed: 'left',\n      },\n      {\n        title: t('group'),\n        dataIndex: 'group',\n        sorter: (a, b, sortOrder) => sortTable(sortOrder, 'group'),\n        width: 150,\n        fixed: 'left',\n      },\n      ...languages\n        .filter((item) => (locale ? item.locale === locale : true))\n        .map((item) => ({\n          key: item.locale,\n          title: item.title,\n          dataIndex: `value[${item.locale}]`,\n          editable: true,\n          width: 300,\n        })),\n    ],\n    [languages, locale],\n  );\n\n  function sortTable(type, column) {\n    let sortType;\n    switch (type) {\n      case 'ascend':\n        sortType = 'asc';\n        break;\n      case 'descend':\n        sortType = 'desc';\n        break;\n\n      default:\n        break;\n    }\n    setSort(sortType);\n    setColumn(column);\n  }\n\n  function fetchTranslations() {\n    setLoading(true);\n    const params = {\n      perPage: pageSize,\n      skip: skipPage,\n      group,\n      sort,\n      column,\n      search,\n    };\n    translationService\n      .getAll(params)\n      .then(({ data }) => {\n        const translations = Object.entries(data.translations).map((item) => ({\n          key: item[0],\n          group: item[1][0].group,\n          ...Object.assign(\n            {},\n            ...languages.map((lang) => ({\n              [`value[${lang.locale}]`]: item[1].find(\n                (el) => el.locale === lang.locale,\n              )?.value,\n            })),\n          ),\n        }));\n        setList(translations);\n        setTotal(data.total);\n      })\n      .finally(() => setLoading(false));\n  }\n\n  useEffect(() => {\n    fetchTranslations();\n    // eslint-disable-next-line\n  }, [pageSize, group, sort, column, skipPage, search]);\n\n  const onChangePagination = (pageNumber) => {\n    const { pageSize, current } = pageNumber;\n    const skip = (current - 1) * pageSize;\n    setPageSize(pageSize);\n    setPage(current);\n    setSkipPage(skip);\n  };\n\n  const handleSave = (row) => {\n    const { dataIndex, key } = row;\n    const newData = [...list];\n    const index = newData.findIndex((item) => row.key === item.key);\n    const item = newData[index];\n    if (item[dataIndex] === row[dataIndex]) {\n      return;\n    }\n    newData.splice(index, 1, { ...item, ...row });\n    setList(newData);\n    const savedItem = {\n      ...row,\n      value: undefined,\n      dataIndex: undefined,\n      key: undefined,\n    };\n    updateTranslation(key, savedItem);\n  };\n\n  function updateTranslation(key, data) {\n    translationService\n      .update(key, data)\n      .then((res) => toast.success(res.message));\n  }\n\n  const components = {\n    body: {\n      row: EditableRow,\n      cell: EditableCell,\n    },\n  };\n\n  const columns = defaultColumns.map((col) => {\n    if (!col.editable) {\n      return col;\n    }\n\n    return {\n      ...col,\n      onCell: (record) => ({\n        record,\n        editable: col.editable,\n        dataIndex: col.dataIndex,\n        title: col.title,\n        fixed: col.fixed,\n        handleSave,\n      }),\n    };\n  });\n\n  const excelExport = () => {\n    setDownloading(true);\n    translationService\n      .export()\n      .then((res) => {\n        window.location.href = export_url + res.data.file_name;\n      })\n      .finally(() => setDownloading(false));\n  };\n\n  const goToImport = () => {\n    dispatch(\n      addMenu({\n        id: 'translation-import',\n        url: `settings/translations/import`,\n        name: t('translation.import'),\n      }),\n    );\n    navigate(`import`);\n  };\n\n  return (\n    <Card\n      extra={\n        <Space wrap>\n          <SearchInput\n            placeholder={t('search')}\n            handleChange={(search) => setSearch(search)}\n          />\n          <Select\n            style={{ minWidth: 150 }}\n            value={locale}\n            onChange={(value) => setLocale(value)}\n            placeholder={t('select.language')}\n          >\n            <Select.Option value=''>{t('all')}</Select.Option>\n            {languages.map((item) => (\n              <Select.Option key={item.locale} value={item.locale}>\n                {item.title}\n              </Select.Option>\n            ))}\n          </Select>\n          <Select\n            style={{ minWidth: 150 }}\n            value={group}\n            onChange={(value) => setGroup(value)}\n            placeholder={t('select.group')}\n          >\n            <Select.Option value=''>{t('all')}</Select.Option>\n            <Select.Option value='web'>{t('web')}</Select.Option>\n            <Select.Option value='mobile'>{t('mobile')}</Select.Option>\n            <Select.Option value='errors'>{t('errors')}</Select.Option>\n          </Select>\n          <Button onClick={excelExport} loading={downloading}>\n            <CgImport className='mr-2' />\n            {t('export')}\n          </Button>\n          <Button onClick={goToImport}>\n            <CgImport className='mr-2' />\n            {t('import')}\n          </Button>\n          <Button\n            icon={<PlusCircleOutlined />}\n            type='primary'\n            onClick={() => setVisible(true)}\n          >\n            {t('add.translation')}\n          </Button>\n        </Space>\n      }\n    >\n      <Table\n        components={components}\n        columns={columns}\n        dataSource={list}\n        pagination={{\n          pageSize,\n          page,\n          total,\n        }}\n        rowKey={(record) => record.key}\n        onChange={onChangePagination}\n        loading={loading}\n        scroll={{\n          x: 1500,\n        }}\n      />\n      {visible && (\n        <TranslationCreateModal\n          visible={visible}\n          setVisible={setVisible}\n          languages={languages}\n          refetch={fetchTranslations}\n        />\n      )}\n    </Card>\n  );\n}\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC/E,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACtE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,mBAAmB;AACpE,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,gBAAG7B,KAAK,CAAC8B,aAAa,CAAC,IAAI,CAAC;AAEjD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,KAAK;EAAE,GAAGC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAM,CAACC,IAAI,CAAC,GAAG3B,IAAI,CAAC4B,OAAO,CAAC,CAAC;EAC7B,oBACER,OAAA,CAACpB,IAAI;IAAC2B,IAAI,EAAEA,IAAK;IAACE,SAAS,EAAE,KAAM;IAAAC,QAAA,eACjCV,OAAA,CAACC,eAAe,CAACU,QAAQ;MAACC,KAAK,EAAEL,IAAK;MAAAG,QAAA,eACpCV,OAAA;QAAA,GAAQK;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEX,CAAC;AAACV,EAAA,CATIH,WAAW;EAAA,QACAvB,IAAI,CAAC4B,OAAO;AAAA;AAAAS,EAAA,GADvBd,WAAW;AAWjB,MAAMe,YAAY,GAAGA,CAAC;EACpBC,KAAK;EACLC,QAAQ;EACRV,QAAQ;EACRW,SAAS;EACTC,MAAM;EACNC,UAAU;EACV,GAAGC;AACL,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAEC;EAAE,CAAC,GAAGjC,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMoD,QAAQ,GAAGrD,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM+B,IAAI,GAAGlC,UAAU,CAAC4B,eAAe,CAAC;EACxC3B,SAAS,CAAC,MAAM;IACd,IAAIqD,OAAO,EAAE;MACXE,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EAEb,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACvBJ,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBpB,IAAI,CAAC0B,cAAc,CAAC;MAClB,CAACZ,SAAS,GAAGC,MAAM,CAACD,SAAS;IAC/B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMa,IAAI,GAAG,MAAAA,CAAA,KAAY;IACvB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5B,IAAI,CAAC6B,cAAc,CAAC,CAAC;MAC1CJ,UAAU,CAAC,CAAC;MACZT,UAAU,CAAC;QAAE,GAAGD,MAAM;QAAE,GAAGa,MAAM;QAAEd;MAAU,CAAC,CAAC;IACjD,CAAC,CAAC,OAAOgB,OAAO,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;IACtC;EACF,CAAC;EAED,IAAIG,SAAS,GAAG9B,QAAQ;EAExB,IAAIU,QAAQ,EAAE;IACZoB,SAAS,GAAGb,OAAO,gBACjB3B,OAAA,CAACpB,IAAI,CAAC6D,IAAI;MACRC,KAAK,EAAE;QACLC,MAAM,EAAE;MACV,CAAE;MACFC,IAAI,EAAEvB,SAAU;MAChBwB,KAAK,EAAE,CACL;QACEC,SAASA,CAACC,CAAC,EAAEnC,KAAK,EAAE;UAClB,IAAI,CAACA,KAAK,EAAE;YACV,OAAOoC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACxB,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;UACjD,CAAC,MAAM,IAAId,KAAK,IAAI,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuC,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;YACxC,OAAOH,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACxB,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM,IAAId,KAAK,IAAI,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuC,IAAI,CAAC,CAAC,CAACC,MAAM,IAAG,CAAC,EAAE;YAC5C,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACxB,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;UAC3D;UACA,OAAOsB,OAAO,CAACK,OAAO,CAAC,CAAC;QAC1B;MACF,CAAC,CACD;MAAA3C,QAAA,eAEFV,OAAA,CAACnB,KAAK;QAACyE,GAAG,EAAEzB,QAAS;QAAC0B,YAAY,EAAErB,IAAK;QAACsB,MAAM,EAAEtB;MAAK;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,gBAEZhB,OAAA;MACEyD,SAAS,EAAC,2FAA2F;MACrGf,KAAK,EAAE;QACLgB,YAAY,EAAE;MAChB,CAAE;MACFC,OAAO,EAAE3B,UAAW;MAAAtB,QAAA,gBAEpBV,OAAA;QAAKyD,SAAS,EAAC,OAAO;QAAA/C,QAAA,EAAEA;MAAQ;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvChB,OAAA,CAACb,YAAY;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACN;EACH;EAEA,oBAAOhB,OAAA;IAAA,GAAQwB,SAAS;IAAAd,QAAA,EAAG8B;EAAS;IAAA3B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC5C,CAAC;AAACS,GAAA,CA7EIP,YAAY;EAAA,QASFzB,cAAc;AAAA;AAAAmE,GAAA,GATxB1C,YAAY;AA+ElB,eAAe,SAAS2C,YAAYA,CAAA,EAAG;EAAAC,GAAA;EACrC,MAAM;IAAEpC;EAAE,CAAC,GAAGjC,cAAc,CAAC,CAAC;EAC9B,MAAMsE,QAAQ,GAAGxE,WAAW,CAAC,CAAC;EAC9B,MAAMyE,QAAQ,GAAGnE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoE,IAAI,EAAEC,OAAO,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC0F,QAAQ,EAAEC,WAAW,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4F,IAAI,EAAEC,OAAO,CAAC,GAAG7F,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC8F,KAAK,EAAEC,QAAQ,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACgG,OAAO,EAAEC,UAAU,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkG,KAAK,EAAEC,QAAQ,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoG,IAAI,EAAEC,OAAO,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACsG,MAAM,EAAEC,SAAS,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwG,OAAO,EAAEC,UAAU,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0G,QAAQ,EAAEC,WAAW,CAAC,GAAG3G,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM;IAAE4G;EAAU,CAAC,GAAG7F,WAAW,CAAE8F,KAAK,IAAKA,KAAK,CAACC,QAAQ,EAAEjG,YAAY,CAAC;EAC1E,MAAM,CAACkG,MAAM,EAAEC,SAAS,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiH,MAAM,EAAEC,SAAS,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmH,WAAW,EAAEC,cAAc,CAAC,GAAGpH,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMqH,cAAc,GAAGvH,OAAO,CAC5B,MAAM,CACJ;IACE4C,KAAK,EAAEO,CAAC,CAAC,MAAM,CAAC;IAChBL,SAAS,EAAE,KAAK;IAChB0E,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAEC,SAAS,KAAKC,SAAS,CAACD,SAAS,EAAE,KAAK,CAAC;IACxDE,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACElF,KAAK,EAAEO,CAAC,CAAC,OAAO,CAAC;IACjBL,SAAS,EAAE,OAAO;IAClB0E,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAEC,SAAS,KAAKC,SAAS,CAACD,SAAS,EAAE,OAAO,CAAC;IAC1DE,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD,GAAGhB,SAAS,CACTiB,MAAM,CAAEC,IAAI,IAAMf,MAAM,GAAGe,IAAI,CAACf,MAAM,KAAKA,MAAM,GAAG,IAAK,CAAC,CAC1DgB,GAAG,CAAED,IAAI,KAAM;IACdE,GAAG,EAAEF,IAAI,CAACf,MAAM;IAChBrE,KAAK,EAAEoF,IAAI,CAACpF,KAAK;IACjBE,SAAS,EAAG,SAAQkF,IAAI,CAACf,MAAO,GAAE;IAClCpE,QAAQ,EAAE,IAAI;IACdgF,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,CACN,EACD,CAACf,SAAS,EAAEG,MAAM,CACpB,CAAC;EAED,SAASW,SAASA,CAACO,IAAI,EAAE3B,MAAM,EAAE;IAC/B,IAAI4B,QAAQ;IACZ,QAAQD,IAAI;MACV,KAAK,QAAQ;QACXC,QAAQ,GAAG,KAAK;QAChB;MACF,KAAK,SAAS;QACZA,QAAQ,GAAG,MAAM;QACjB;MAEF;QACE;IACJ;IACA7B,OAAO,CAAC6B,QAAQ,CAAC;IACjB3B,SAAS,CAACD,MAAM,CAAC;EACnB;EAEA,SAAS6B,iBAAiBA,CAAA,EAAG;IAC3BlC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMmC,MAAM,GAAG;MACbC,OAAO,EAAE3C,QAAQ;MACjB4C,IAAI,EAAE5B,QAAQ;MACdR,KAAK;MACLE,IAAI;MACJE,MAAM;MACNW;IACF,CAAC;IACDzG,kBAAkB,CACf+H,MAAM,CAACH,MAAM,CAAC,CACdI,IAAI,CAAC,CAAC;MAAEC;IAAK,CAAC,KAAK;MAClB,MAAMC,YAAY,GAAGC,MAAM,CAACC,OAAO,CAACH,IAAI,CAACC,YAAY,CAAC,CAACX,GAAG,CAAED,IAAI,KAAM;QACpEE,GAAG,EAAEF,IAAI,CAAC,CAAC,CAAC;QACZ5B,KAAK,EAAE4B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC5B,KAAK;QACvB,GAAGyC,MAAM,CAACE,MAAM,CACd,CAAC,CAAC,EACF,GAAGjC,SAAS,CAACmB,GAAG,CAAEe,IAAI;UAAA,IAAAC,YAAA;UAAA,OAAM;YAC1B,CAAE,SAAQD,IAAI,CAAC/B,MAAO,GAAE,IAAAgC,YAAA,GAAGjB,IAAI,CAAC,CAAC,CAAC,CAACkB,IAAI,CACpCC,EAAE,IAAKA,EAAE,CAAClC,MAAM,KAAK+B,IAAI,CAAC/B,MAC7B,CAAC,cAAAgC,YAAA,uBAF0BA,YAAA,CAExB5G;UACL,CAAC;QAAA,CAAC,CACJ;MACF,CAAC,CAAC,CAAC;MACHsD,OAAO,CAACiD,YAAY,CAAC;MACrB3C,QAAQ,CAAC0C,IAAI,CAAC3C,KAAK,CAAC;IACtB,CAAC,CAAC,CACDoD,OAAO,CAAC,MAAMjD,UAAU,CAAC,KAAK,CAAC,CAAC;EACrC;EAEApG,SAAS,CAAC,MAAM;IACdsI,iBAAiB,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACzC,QAAQ,EAAEQ,KAAK,EAAEE,IAAI,EAAEE,MAAM,EAAEI,QAAQ,EAAEO,MAAM,CAAC,CAAC;EAErD,MAAMkC,kBAAkB,GAAIC,UAAU,IAAK;IACzC,MAAM;MAAE1D,QAAQ;MAAErC;IAAQ,CAAC,GAAG+F,UAAU;IACxC,MAAMd,IAAI,GAAG,CAACjF,OAAO,GAAG,CAAC,IAAIqC,QAAQ;IACrCC,WAAW,CAACD,QAAQ,CAAC;IACrBG,OAAO,CAACxC,OAAO,CAAC;IAChBsD,WAAW,CAAC2B,IAAI,CAAC;EACnB,CAAC;EAED,MAAMxF,UAAU,GAAIuG,GAAG,IAAK;IAC1B,MAAM;MAAEzG,SAAS;MAAEoF;IAAI,CAAC,GAAGqB,GAAG;IAC9B,MAAMC,OAAO,GAAG,CAAC,GAAG9D,IAAI,CAAC;IACzB,MAAM7D,KAAK,GAAG2H,OAAO,CAACC,SAAS,CAAEzB,IAAI,IAAKuB,GAAG,CAACrB,GAAG,KAAKF,IAAI,CAACE,GAAG,CAAC;IAC/D,MAAMF,IAAI,GAAGwB,OAAO,CAAC3H,KAAK,CAAC;IAC3B,IAAImG,IAAI,CAAClF,SAAS,CAAC,KAAKyG,GAAG,CAACzG,SAAS,CAAC,EAAE;MACtC;IACF;IACA0G,OAAO,CAACE,MAAM,CAAC7H,KAAK,EAAE,CAAC,EAAE;MAAE,GAAGmG,IAAI;MAAE,GAAGuB;IAAI,CAAC,CAAC;IAC7C5D,OAAO,CAAC6D,OAAO,CAAC;IAChB,MAAMG,SAAS,GAAG;MAChB,GAAGJ,GAAG;MACNlH,KAAK,EAAEuH,SAAS;MAChB9G,SAAS,EAAE8G,SAAS;MACpB1B,GAAG,EAAE0B;IACP,CAAC;IACDC,iBAAiB,CAAC3B,GAAG,EAAEyB,SAAS,CAAC;EACnC,CAAC;EAED,SAASE,iBAAiBA,CAAC3B,GAAG,EAAES,IAAI,EAAE;IACpCjI,kBAAkB,CACfoJ,MAAM,CAAC5B,GAAG,EAAES,IAAI,CAAC,CACjBD,IAAI,CAAEqB,GAAG,IAAKpJ,KAAK,CAACqJ,OAAO,CAACD,GAAG,CAACE,OAAO,CAAC,CAAC;EAC9C;EAEA,MAAMC,UAAU,GAAG;IACjBC,IAAI,EAAE;MACJZ,GAAG,EAAE3H,WAAW;MAChBwI,IAAI,EAAEzH;IACR;EACF,CAAC;EAED,MAAM0H,OAAO,GAAG9C,cAAc,CAACU,GAAG,CAAEqC,GAAG,IAAK;IAC1C,IAAI,CAACA,GAAG,CAACzH,QAAQ,EAAE;MACjB,OAAOyH,GAAG;IACZ;IAEA,OAAO;MACL,GAAGA,GAAG;MACNC,MAAM,EAAGxH,MAAM,KAAM;QACnBA,MAAM;QACNF,QAAQ,EAAEyH,GAAG,CAACzH,QAAQ;QACtBC,SAAS,EAAEwH,GAAG,CAACxH,SAAS;QACxBF,KAAK,EAAE0H,GAAG,CAAC1H,KAAK;QAChBkF,KAAK,EAAEwC,GAAG,CAACxC,KAAK;QAChB9E;MACF,CAAC;IACH,CAAC;EACH,CAAC,CAAC;EAEF,MAAMwH,WAAW,GAAGA,CAAA,KAAM;IACxBlD,cAAc,CAAC,IAAI,CAAC;IACpB5G,kBAAkB,CACf+J,MAAM,CAAC,CAAC,CACR/B,IAAI,CAAEqB,GAAG,IAAK;MACbW,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGrJ,UAAU,GAAGwI,GAAG,CAACpB,IAAI,CAACkC,SAAS;IACxD,CAAC,CAAC,CACDzB,OAAO,CAAC,MAAM9B,cAAc,CAAC,KAAK,CAAC,CAAC;EACzC,CAAC;EAED,MAAMwD,UAAU,GAAGA,CAAA,KAAM;IACvBtF,QAAQ,CACNnE,OAAO,CAAC;MACN0J,EAAE,EAAE,oBAAoB;MACxBC,GAAG,EAAG,8BAA6B;MACnC3G,IAAI,EAAElB,CAAC,CAAC,oBAAoB;IAC9B,CAAC,CACH,CAAC;IACDsC,QAAQ,CAAE,QAAO,CAAC;EACpB,CAAC;EAED,oBACEhE,OAAA,CAACrB,IAAI;IACH6K,KAAK,eACHxJ,OAAA,CAACjB,KAAK;MAAC0K,IAAI;MAAA/I,QAAA,gBACTV,OAAA,CAACN,WAAW;QACVgK,WAAW,EAAEhI,CAAC,CAAC,QAAQ,CAAE;QACzBiI,YAAY,EAAGjE,MAAM,IAAKC,SAAS,CAACD,MAAM;MAAE;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACFhB,OAAA,CAAClB,MAAM;QACL4D,KAAK,EAAE;UAAEkH,QAAQ,EAAE;QAAI,CAAE;QACzBhJ,KAAK,EAAE4E,MAAO;QACdqE,QAAQ,EAAGjJ,KAAK,IAAK6E,SAAS,CAAC7E,KAAK,CAAE;QACtC8I,WAAW,EAAEhI,CAAC,CAAC,iBAAiB,CAAE;QAAAhB,QAAA,gBAElCV,OAAA,CAAClB,MAAM,CAACgL,MAAM;UAAClJ,KAAK,EAAC,EAAE;UAAAF,QAAA,EAAEgB,CAAC,CAAC,KAAK;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,EACjDqE,SAAS,CAACmB,GAAG,CAAED,IAAI,iBAClBvG,OAAA,CAAClB,MAAM,CAACgL,MAAM;UAAmBlJ,KAAK,EAAE2F,IAAI,CAACf,MAAO;UAAA9E,QAAA,EACjD6F,IAAI,CAACpF;QAAK,GADOoF,IAAI,CAACf,MAAM;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhB,CAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACThB,OAAA,CAAClB,MAAM;QACL4D,KAAK,EAAE;UAAEkH,QAAQ,EAAE;QAAI,CAAE;QACzBhJ,KAAK,EAAE+D,KAAM;QACbkF,QAAQ,EAAGjJ,KAAK,IAAKgE,QAAQ,CAAChE,KAAK,CAAE;QACrC8I,WAAW,EAAEhI,CAAC,CAAC,cAAc,CAAE;QAAAhB,QAAA,gBAE/BV,OAAA,CAAClB,MAAM,CAACgL,MAAM;UAAClJ,KAAK,EAAC,EAAE;UAAAF,QAAA,EAAEgB,CAAC,CAAC,KAAK;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAClDhB,OAAA,CAAClB,MAAM,CAACgL,MAAM;UAAClJ,KAAK,EAAC,KAAK;UAAAF,QAAA,EAAEgB,CAAC,CAAC,KAAK;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eACrDhB,OAAA,CAAClB,MAAM,CAACgL,MAAM;UAAClJ,KAAK,EAAC,QAAQ;UAAAF,QAAA,EAAEgB,CAAC,CAAC,QAAQ;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAC3DhB,OAAA,CAAClB,MAAM,CAACgL,MAAM;UAAClJ,KAAK,EAAC,QAAQ;UAAAF,QAAA,EAAEgB,CAAC,CAAC,QAAQ;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACThB,OAAA,CAACtB,MAAM;QAACiF,OAAO,EAAEoF,WAAY;QAACtE,OAAO,EAAEmB,WAAY;QAAAlF,QAAA,gBACjDV,OAAA,CAACL,QAAQ;UAAC8D,SAAS,EAAC;QAAM;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC5BU,CAAC,CAAC,QAAQ,CAAC;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACThB,OAAA,CAACtB,MAAM;QAACiF,OAAO,EAAE0F,UAAW;QAAA3I,QAAA,gBAC1BV,OAAA,CAACL,QAAQ;UAAC8D,SAAS,EAAC;QAAM;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC5BU,CAAC,CAAC,QAAQ,CAAC;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACThB,OAAA,CAACtB,MAAM;QACLqL,IAAI,eAAE/J,OAAA,CAACZ,kBAAkB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7B0F,IAAI,EAAC,SAAS;QACd/C,OAAO,EAAEA,CAAA,KAAMuB,UAAU,CAAC,IAAI,CAAE;QAAAxE,QAAA,EAE/BgB,CAAC,CAAC,iBAAiB;MAAC;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACR;IAAAN,QAAA,gBAEDV,OAAA,CAAChB,KAAK;MACJyJ,UAAU,EAAEA,UAAW;MACvBG,OAAO,EAAEA,OAAQ;MACjBoB,UAAU,EAAE/F,IAAK;MACjBgG,UAAU,EAAE;QACV9F,QAAQ;QACRE,IAAI;QACJE;MACF,CAAE;MACF2F,MAAM,EAAG5I,MAAM,IAAKA,MAAM,CAACmF,GAAI;MAC/BoD,QAAQ,EAAEjC,kBAAmB;MAC7BnD,OAAO,EAAEA,OAAQ;MACjB0F,MAAM,EAAE;QACNC,CAAC,EAAE;MACL;IAAE;MAAAvJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACDiE,OAAO,iBACNjF,OAAA,CAACX,sBAAsB;MACrB4F,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAEA,UAAW;MACvBG,SAAS,EAAEA,SAAU;MACrBgF,OAAO,EAAEzD;IAAkB;MAAA/F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX;AAAC8C,GAAA,CAhQuBD,YAAY;EAAA,QACpBpE,cAAc,EACXF,WAAW,EACXM,WAAW,EAWNL,WAAW;AAAA;AAAA8K,GAAA,GAdXzG,YAAY;AAAA,IAAA5C,EAAA,EAAA2C,GAAA,EAAA0G,GAAA;AAAAC,YAAA,CAAAtJ,EAAA;AAAAsJ,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}