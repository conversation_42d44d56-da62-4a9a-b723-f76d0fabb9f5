{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\infinite-select.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useEffect, useRef } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InfiniteSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  hasMore,\n  refetchOnFocus = false,\n  ...props\n}) => {\n  _s();\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [search, setSearch] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const debounceFetcher = useMemo(() => {\n    const loadOptions = value => {\n      setOptions([]);\n      setSearch(value);\n      setFetching(true);\n      fetchOptions({\n        search: value\n      }).then(newOptions => {\n        setOptions(newOptions);\n        setCurrentPage(2);\n        setFetching(false);\n      }).finally(() => setLoading(false));\n    };\n    return debounce(loadOptions, debounceTimeout);\n  }, [fetchOptions, debounceTimeout, currentPage]);\n  const fetchOnFocus = () => {\n    if (refetchOnFocus) {\n      debounceFetcher('');\n    } else {\n      if (!(options !== null && options !== void 0 && options.length)) {\n        debounceFetcher('');\n      }\n    }\n  };\n  const onScroll = async event => {\n    const target = event.target;\n    if (!loading && target.scrollTop + target.offsetHeight === target.scrollHeight) {\n      if (hasMore) {\n        setLoading(true);\n        // target.scrollTo(0, target.scrollHeight);\n        fetchOptions({\n          search: search,\n          page: currentPage\n        }).then(item => {\n          setCurrentPage(i => i + 1);\n          setOptions([...options, ...item]);\n        }).finally(() => setLoading(false));\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Select, {\n    showSearch: true,\n    allowClear: true,\n    onPopupScroll: onScroll,\n    labelInValue: true,\n    filterOption: false,\n    onSearch: debounceFetcher,\n    notFoundContent: fetching ? /*#__PURE__*/_jsxDEV(Spin, {\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 35\n    }, this) : 'no results',\n    onFocus: fetchOnFocus,\n    ...props,\n    children: [options.map((item, index) => /*#__PURE__*/_jsxDEV(Select.Option, {\n      value: item.value,\n      children: item.label\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this)), loading && /*#__PURE__*/_jsxDEV(Select.Option, {\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(InfiniteSelect, \"/My8Gqubbxe1OGR9KUEyNVs4MzU=\");\n_c = InfiniteSelect;\nvar _c;\n$RefreshReg$(_c, \"InfiniteSelect\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useEffect", "useRef", "debounce", "Select", "Spin", "jsxDEV", "_jsxDEV", "InfiniteSelect", "fetchOptions", "debounceTimeout", "hasMore", "refetchOnFocus", "props", "_s", "fetching", "setFetching", "options", "setOptions", "loading", "setLoading", "search", "setSearch", "currentPage", "setCurrentPage", "deboun<PERSON><PERSON><PERSON><PERSON>", "loadOptions", "value", "then", "newOptions", "finally", "fetchOnFocus", "length", "onScroll", "event", "target", "scrollTop", "offsetHeight", "scrollHeight", "page", "item", "i", "showSearch", "allowClear", "onPopupScroll", "labelInValue", "filterOption", "onSearch", "notFoundContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onFocus", "children", "map", "index", "Option", "label", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/infinite-select.js"], "sourcesContent": ["import React, { useState, useMemo, useEffect, useRef } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\n\nexport const InfiniteSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  hasMore,\n  refetchOnFocus = false,\n  ...props\n}) => {\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [search, setSearch] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n\n  const debounceFetcher = useMemo(() => {\n    const loadOptions = (value) => {\n      setOptions([]);\n      setSearch(value);\n      setFetching(true);\n      fetchOptions({ search: value })\n        .then((newOptions) => {\n          setOptions(newOptions);\n          setCurrentPage(2);\n          setFetching(false);\n        })\n        .finally(() => setLoading(false));\n    };\n    return debounce(loadOptions, debounceTimeout);\n  }, [fetchOptions, debounceTimeout, currentPage]);\n\n  const fetchOnFocus = () => {\n    if (refetchOnFocus) {\n      debounceFetcher('');\n    } else {\n      if (!options?.length) {\n        debounceFetcher('');\n      }\n    }\n  };\n\n  const onScroll = async (event) => {\n    const target = event.target;\n    if (\n      !loading &&\n      target.scrollTop + target.offsetHeight === target.scrollHeight\n    ) {\n      if (hasMore) {\n        setLoading(true);\n        // target.scrollTo(0, target.scrollHeight);\n        fetchOptions({ search: search, page: currentPage })\n          .then((item) => {\n            setCurrentPage((i) => i + 1);\n            setOptions([...options, ...item]);\n          })\n          .finally(() => setLoading(false));\n      }\n    }\n  };\n\n  return (\n    <Select\n      showSearch\n      allowClear\n      onPopupScroll={onScroll}\n      labelInValue={true}\n      filterOption={false}\n      onSearch={debounceFetcher}\n      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}\n      onFocus={fetchOnFocus}\n      {...props}\n    >\n      {options.map((item, index) => (\n        <Select.Option key={index} value={item.value}>\n          {item.label}\n        </Select.Option>\n      ))}\n      {loading && (\n        <Select.Option>\n          <Spin size='small' />\n        </Select.Option>\n      )}\n    </Select>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BC,YAAY;EACZC,eAAe,GAAG,GAAG;EACrBC,OAAO;EACPC,cAAc,GAAG,KAAK;EACtB,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAM0B,eAAe,GAAGzB,OAAO,CAAC,MAAM;IACpC,MAAM0B,WAAW,GAAIC,KAAK,IAAK;MAC7BT,UAAU,CAAC,EAAE,CAAC;MACdI,SAAS,CAACK,KAAK,CAAC;MAChBX,WAAW,CAAC,IAAI,CAAC;MACjBP,YAAY,CAAC;QAAEY,MAAM,EAAEM;MAAM,CAAC,CAAC,CAC5BC,IAAI,CAAEC,UAAU,IAAK;QACpBX,UAAU,CAACW,UAAU,CAAC;QACtBL,cAAc,CAAC,CAAC,CAAC;QACjBR,WAAW,CAAC,KAAK,CAAC;MACpB,CAAC,CAAC,CACDc,OAAO,CAAC,MAAMV,UAAU,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IACD,OAAOjB,QAAQ,CAACuB,WAAW,EAAEhB,eAAe,CAAC;EAC/C,CAAC,EAAE,CAACD,YAAY,EAAEC,eAAe,EAAEa,WAAW,CAAC,CAAC;EAEhD,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInB,cAAc,EAAE;MAClBa,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,MAAM;MACL,IAAI,EAACR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEe,MAAM,GAAE;QACpBP,eAAe,CAAC,EAAE,CAAC;MACrB;IACF;EACF,CAAC;EAED,MAAMQ,QAAQ,GAAG,MAAOC,KAAK,IAAK;IAChC,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IACE,CAAChB,OAAO,IACRgB,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACE,YAAY,KAAKF,MAAM,CAACG,YAAY,EAC9D;MACA,IAAI3B,OAAO,EAAE;QACXS,UAAU,CAAC,IAAI,CAAC;QAChB;QACAX,YAAY,CAAC;UAAEY,MAAM,EAAEA,MAAM;UAAEkB,IAAI,EAAEhB;QAAY,CAAC,CAAC,CAChDK,IAAI,CAAEY,IAAI,IAAK;UACdhB,cAAc,CAAEiB,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;UAC5BvB,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE,GAAGuB,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CACDV,OAAO,CAAC,MAAMV,UAAU,CAAC,KAAK,CAAC,CAAC;MACrC;IACF;EACF,CAAC;EAED,oBACEb,OAAA,CAACH,MAAM;IACLsC,UAAU;IACVC,UAAU;IACVC,aAAa,EAAEX,QAAS;IACxBY,YAAY,EAAE,IAAK;IACnBC,YAAY,EAAE,KAAM;IACpBC,QAAQ,EAAEtB,eAAgB;IAC1BuB,eAAe,EAAEjC,QAAQ,gBAAGR,OAAA,CAACF,IAAI;MAAC4C,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,YAAa;IACjEC,OAAO,EAAEvB,YAAa;IAAA,GAClBlB,KAAK;IAAA0C,QAAA,GAERtC,OAAO,CAACuC,GAAG,CAAC,CAAChB,IAAI,EAAEiB,KAAK,kBACvBlD,OAAA,CAACH,MAAM,CAACsD,MAAM;MAAa/B,KAAK,EAAEa,IAAI,CAACb,KAAM;MAAA4B,QAAA,EAC1Cf,IAAI,CAACmB;IAAK,GADOF,KAAK;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CAChB,CAAC,EACDlC,OAAO,iBACNZ,OAAA,CAACH,MAAM,CAACsD,MAAM;MAAAH,QAAA,eACZhD,OAAA,CAACF,IAAI;QAAC4C,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAChB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACvC,EAAA,CAlFWN,cAAc;AAAAoD,EAAA,GAAdpD,cAAc;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}