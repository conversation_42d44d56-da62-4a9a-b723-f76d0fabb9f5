{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\addons\\\\productStatusModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Button, Col, Form, Modal, Row, Select } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport productService from '../../services/product';\nimport { setRefetch } from '../../redux/slices/menu';\nimport { fetchAddons } from '../../redux/slices/addons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst allStatuses = ['published', 'pending', 'unpublished'];\nexport default function ProductStatusModal({\n  orderDetails: data,\n  handleCancel\n}) {\n  _s();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const {\n    t\n  } = useTranslation();\n  const [loading, setLoading] = useState(false);\n  const isMountedRef = useRef(true);\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  const onFinish = values => {\n    if (!isMountedRef.current) return;\n    setLoading(true);\n    const params = {\n      ...values\n    };\n    productService.updateStatus(data.uuid, params).then(() => {\n      if (isMountedRef.current) {\n        handleCancel();\n        const data = activeMenu.data;\n        const paramsData = {\n          status: (data === null || data === void 0 ? void 0 : data.role) === 'deleted_at' ? null : (data === null || data === void 0 ? void 0 : data.role) || 'published',\n          deleted_at: (data === null || data === void 0 ? void 0 : data.role) === 'deleted_at' ? data.role : null,\n          perPage: data === null || data === void 0 ? void 0 : data.perPage,\n          page: data === null || data === void 0 ? void 0 : data.page\n        };\n        dispatch(fetchAddons(paramsData));\n        dispatch(setRefetch(activeMenu));\n      }\n    }).finally(() => {\n      if (isMountedRef.current) {\n        setLoading(false);\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    visible: !!data,\n    title: data.title,\n    onCancel: handleCancel,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => form.submit(),\n      loading: loading,\n      children: t('save')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"default\",\n      onClick: handleCancel,\n      children: t('cancel')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      layout: \"vertical\",\n      onFinish: onFinish,\n      initialValues: {\n        status: data.status\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 12,\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: t('status'),\n            name: \"status\",\n            rules: [{\n              required: true,\n              message: t('required')\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              children: allStatuses.map((item, idx) => /*#__PURE__*/_jsxDEV(Select.Option, {\n                value: item,\n                children: t(item)\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductStatusModal, \"r3hK3LaeDJXz1NLcpMbeRZtECXE=\", false, function () {\n  return [useSelector, Form.useForm, useDispatch, useTranslation];\n});\n_c = ProductStatusModal;\nvar _c;\n$RefreshReg$(_c, \"ProductStatusModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "<PERSON><PERSON>", "Col", "Form", "Modal", "Row", "Select", "shallowEqual", "useDispatch", "useSelector", "useTranslation", "productService", "setRefetch", "fetchAddons", "jsxDEV", "_jsxDEV", "allStatuses", "ProductStatusModal", "orderDetails", "data", "handleCancel", "_s", "activeMenu", "state", "menu", "form", "useForm", "dispatch", "t", "loading", "setLoading", "isMountedRef", "current", "onFinish", "values", "params", "updateStatus", "uuid", "then", "paramsData", "status", "role", "deleted_at", "perPage", "page", "finally", "visible", "title", "onCancel", "footer", "type", "onClick", "submit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "initialValues", "gutter", "span", "<PERSON><PERSON>", "label", "name", "rules", "required", "message", "map", "item", "idx", "Option", "value", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/addons/productStatusModal.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Button, Col, Form, Modal, Row, Select } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport productService from '../../services/product';\nimport { setRefetch } from '../../redux/slices/menu';\nimport { fetchAddons } from '../../redux/slices/addons';\n\nconst allStatuses = ['published', 'pending', 'unpublished'];\n\nexport default function ProductStatusModal({\n  orderDetails: data,\n  handleCancel,\n}) {\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(false);\n  const isMountedRef = useRef(true);\n\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  const onFinish = (values) => {\n    if (!isMountedRef.current) return;\n\n    setLoading(true);\n    const params = { ...values };\n    productService\n      .updateStatus(data.uuid, params)\n      .then(() => {\n        if (isMountedRef.current) {\n          handleCancel();\n          const data = activeMenu.data;\n          const paramsData = {\n            status:\n              data?.role === 'deleted_at' ? null : data?.role || 'published',\n            deleted_at: data?.role === 'deleted_at' ? data.role : null,\n            perPage: data?.perPage,\n            page: data?.page,\n          };\n          dispatch(fetchAddons(paramsData));\n          dispatch(setRefetch(activeMenu));\n        }\n      })\n      .finally(() => {\n        if (isMountedRef.current) {\n          setLoading(false);\n        }\n      });\n  };\n\n  return (\n    <Modal\n      visible={!!data}\n      title={data.title}\n      onCancel={handleCancel}\n      footer={[\n        <Button type='primary' onClick={() => form.submit()} loading={loading}>\n          {t('save')}\n        </Button>,\n        <Button type='default' onClick={handleCancel}>\n          {t('cancel')}\n        </Button>,\n      ]}\n    >\n      <Form\n        form={form}\n        layout='vertical'\n        onFinish={onFinish}\n        initialValues={{ status: data.status }}\n      >\n        <Row gutter={12}>\n          <Col span={24}>\n            <Form.Item\n              label={t('status')}\n              name='status'\n              rules={[\n                {\n                  required: true,\n                  message: t('required'),\n                },\n              ]}\n            >\n              <Select>\n                {allStatuses.map((item, idx) => (\n                  <Select.Option key={idx} value={item}>\n                    {t(item)}\n                  </Select.Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n      </Form>\n    </Modal>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,QAAQ,MAAM;AAC5D,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,WAAW,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC;AAE3D,eAAe,SAASC,kBAAkBA,CAAC;EACzCC,YAAY,EAAEC,IAAI;EAClBC;AACF,CAAC,EAAE;EAAAC,EAAA;EACD,MAAM;IAAEC;EAAW,CAAC,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEjB,YAAY,CAAC;EACvE,MAAM,CAACkB,IAAI,CAAC,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoB;EAAE,CAAC,GAAGlB,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMiC,YAAY,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXgC,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,QAAQ,GAAIC,MAAM,IAAK;IAC3B,IAAI,CAACH,YAAY,CAACC,OAAO,EAAE;IAE3BF,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMK,MAAM,GAAG;MAAE,GAAGD;IAAO,CAAC;IAC5BvB,cAAc,CACXyB,YAAY,CAACjB,IAAI,CAACkB,IAAI,EAAEF,MAAM,CAAC,CAC/BG,IAAI,CAAC,MAAM;MACV,IAAIP,YAAY,CAACC,OAAO,EAAE;QACxBZ,YAAY,CAAC,CAAC;QACd,MAAMD,IAAI,GAAGG,UAAU,CAACH,IAAI;QAC5B,MAAMoB,UAAU,GAAG;UACjBC,MAAM,EACJ,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,IAAI,MAAK,YAAY,GAAG,IAAI,GAAG,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,IAAI,KAAI,WAAW;UAChEC,UAAU,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,IAAI,MAAK,YAAY,GAAGtB,IAAI,CAACsB,IAAI,GAAG,IAAI;UAC1DE,OAAO,EAAExB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,OAAO;UACtBC,IAAI,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB;QACd,CAAC;QACDjB,QAAQ,CAACd,WAAW,CAAC0B,UAAU,CAAC,CAAC;QACjCZ,QAAQ,CAACf,UAAU,CAACU,UAAU,CAAC,CAAC;MAClC;IACF,CAAC,CAAC,CACDuB,OAAO,CAAC,MAAM;MACb,IAAId,YAAY,CAACC,OAAO,EAAE;QACxBF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAED,oBACEf,OAAA,CAACX,KAAK;IACJ0C,OAAO,EAAE,CAAC,CAAC3B,IAAK;IAChB4B,KAAK,EAAE5B,IAAI,CAAC4B,KAAM;IAClBC,QAAQ,EAAE5B,YAAa;IACvB6B,MAAM,EAAE,cACNlC,OAAA,CAACd,MAAM;MAACiD,IAAI,EAAC,SAAS;MAACC,OAAO,EAAEA,CAAA,KAAM1B,IAAI,CAAC2B,MAAM,CAAC,CAAE;MAACvB,OAAO,EAAEA,OAAQ;MAAAwB,QAAA,EACnEzB,CAAC,CAAC,MAAM;IAAC;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACT1C,OAAA,CAACd,MAAM;MAACiD,IAAI,EAAC,SAAS;MAACC,OAAO,EAAE/B,YAAa;MAAAiC,QAAA,EAC1CzB,CAAC,CAAC,QAAQ;IAAC;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,CACT;IAAAJ,QAAA,eAEFtC,OAAA,CAACZ,IAAI;MACHsB,IAAI,EAAEA,IAAK;MACXiC,MAAM,EAAC,UAAU;MACjBzB,QAAQ,EAAEA,QAAS;MACnB0B,aAAa,EAAE;QAAEnB,MAAM,EAAErB,IAAI,CAACqB;MAAO,CAAE;MAAAa,QAAA,eAEvCtC,OAAA,CAACV,GAAG;QAACuD,MAAM,EAAE,EAAG;QAAAP,QAAA,eACdtC,OAAA,CAACb,GAAG;UAAC2D,IAAI,EAAE,EAAG;UAAAR,QAAA,eACZtC,OAAA,CAACZ,IAAI,CAAC2D,IAAI;YACRC,KAAK,EAAEnC,CAAC,CAAC,QAAQ,CAAE;YACnBoC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAE,CACL;cACEC,QAAQ,EAAE,IAAI;cACdC,OAAO,EAAEvC,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YAAAyB,QAAA,eAEFtC,OAAA,CAACT,MAAM;cAAA+C,QAAA,EACJrC,WAAW,CAACoD,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACzBvD,OAAA,CAACT,MAAM,CAACiE,MAAM;gBAAWC,KAAK,EAAEH,IAAK;gBAAAhB,QAAA,EAClCzB,CAAC,CAACyC,IAAI;cAAC,GADUC,GAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAACpC,EAAA,CA3FuBJ,kBAAkB;EAAA,QAIjBR,WAAW,EACnBN,IAAI,CAACuB,OAAO,EACVlB,WAAW,EACdE,cAAc;AAAA;AAAA+D,EAAA,GAPNxD,kBAAkB;AAAA,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}