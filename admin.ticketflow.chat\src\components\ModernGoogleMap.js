import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useGoogleMaps } from './GoogleMapsWrapper';

/**
 * Modern Google Maps component that uses the new AdvancedMarkerElement API
 * instead of the deprecated google.maps.Marker
 */
const ModernGoogleMap = ({
  center = { lat: -18.497665, lng: -50.535224 },
  zoom = 10,
  onClick,
  onReady,
  children,
  className = '',
  style = { height: '400px', width: '100%' },
  options = {},
  ...props
}) => {
  const { google, isReady, error } = useGoogleMaps();
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markersRef = useRef([]);
  const [isMapReady, setIsMapReady] = useState(false);

  // Initialize the map
  useEffect(() => {
    if (!isReady || !google || !mapRef.current || mapInstanceRef.current) {
      return;
    }

    try {
      const mapOptions = {
        center,
        zoom,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        ...options,
      };

      const map = new google.maps.Map(mapRef.current, mapOptions);
      mapInstanceRef.current = map;

      // Add click listener
      if (onClick) {
        map.addListener('click', (event) => {
          onClick(event, map, { latLng: event.latLng });
        });
      }

      // Call onReady callback
      if (onReady) {
        onReady(map, google.maps);
      }

      setIsMapReady(true);
    } catch (err) {
      console.error('Error initializing Google Map:', err);
    }
  }, [isReady, google, center, zoom, onClick, onReady, options]);

  // Update map center when prop changes
  useEffect(() => {
    if (mapInstanceRef.current && center) {
      mapInstanceRef.current.setCenter(center);
    }
  }, [center]);

  // Update map zoom when prop changes
  useEffect(() => {
    if (mapInstanceRef.current && zoom) {
      mapInstanceRef.current.setZoom(zoom);
    }
  }, [zoom]);

  // Clean up markers and map on unmount
  useEffect(() => {
    return () => {
      // Clean up markers
      markersRef.current.forEach(marker => {
        if (marker && marker.map) {
          marker.map = null;
        }
      });
      markersRef.current = [];

      // Clean up map
      if (mapInstanceRef.current) {
        google?.maps?.event?.clearInstanceListeners?.(mapInstanceRef.current);
        mapInstanceRef.current = null;
      }
    };
  }, [google]);

  // Method to add modern markers
  const addAdvancedMarker = useCallback((position, options = {}) => {
    if (!google || !mapInstanceRef.current || !google.maps.marker?.AdvancedMarkerElement) {
      console.warn('AdvancedMarkerElement not available, falling back to regular Marker');
      return addRegularMarker(position, options);
    }

    try {
      const marker = new google.maps.marker.AdvancedMarkerElement({
        map: mapInstanceRef.current,
        position,
        title: options.title || '',
        ...options,
      });

      markersRef.current.push(marker);
      return marker;
    } catch (error) {
      console.warn('Failed to create AdvancedMarkerElement, falling back to regular Marker:', error);
      return addRegularMarker(position, options);
    }
  }, [google]);

  // Fallback method for regular markers
  const addRegularMarker = useCallback((position, options = {}) => {
    if (!google || !mapInstanceRef.current) {
      return null;
    }

    const marker = new google.maps.Marker({
      map: mapInstanceRef.current,
      position,
      title: options.title || '',
      icon: options.icon,
      ...options,
    });

    markersRef.current.push(marker);
    return marker;
  }, [google]);

  // Method to clear all markers
  const clearMarkers = useCallback(() => {
    markersRef.current.forEach(marker => {
      if (marker && marker.map) {
        marker.map = null;
      }
    });
    markersRef.current = [];
  }, []);

  // Expose map methods to parent components
  useEffect(() => {
    if (isMapReady && mapInstanceRef.current) {
      // Attach helper methods to the map instance for external access
      mapInstanceRef.current.addAdvancedMarker = addAdvancedMarker;
      mapInstanceRef.current.addRegularMarker = addRegularMarker;
      mapInstanceRef.current.clearMarkers = clearMarkers;
    }
  }, [isMapReady, addAdvancedMarker, addRegularMarker, clearMarkers]);

  if (error) {
    return (
      <div style={{ ...style, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{ color: 'red', textAlign: 'center' }}>
          Error loading Google Maps: {error.message}
        </div>
      </div>
    );
  }

  if (!isReady) {
    return (
      <div style={{ ...style, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>Loading Google Maps...</div>
      </div>
    );
  }

  return (
    <div className={className} style={style}>
      <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
      {isMapReady && children && 
        React.Children.map(children, child => 
          React.cloneElement(child, { 
            map: mapInstanceRef.current, 
            google,
            addAdvancedMarker,
            addRegularMarker,
            clearMarkers
          })
        )
      }
    </div>
  );
};

export default ModernGoogleMap;
